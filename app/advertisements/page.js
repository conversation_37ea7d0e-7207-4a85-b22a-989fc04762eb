'use client';
import React, { useState, useRef } from 'react';
import dynamic from 'next/dynamic';
import PrivateLayout from '../components/layout/PrivateLayout';
import Sidebar from '../components/sidebar/Sidebar';
import InputField from '../components/Inputs/InputField';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import Link from 'next/link';
import {
  SortableContext,
  verticalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable';

// const CampaignsTable = dynamic(
//   () => import('../components/table/CampaignsTable'),
//   {
//     ssr: false,
//   }
// );

// Add this new component above the Products component
const SortableItem = ({ id, checked, onChange }) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <li
      ref={setNodeRef}
      style={style}
      className="flex items-center gap-2 px-2 py-1 hover:bg-light-500/5 rounded-lg"
    >
      <div className="flex items-center justify-between gap-2 w-full">
        <div className="inline-flex items-center gap-2 flex-1">
          <label
            className="flex items-center cursor-pointer relative"
            htmlFor={`check-${id}`}
          >
            <input
              type="checkbox"
              className="peer h-4 w-4 cursor-pointer transition-all appearance-none rounded border-2 border-gray-200 checked:bg-primary-500 checked:border-primary-500"
              id={`check-${id}`}
              checked={checked}
              onChange={onChange}
            />
            <span className="absolute text-white opacity-0 peer-checked:opacity-100 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-3 w-3"
                viewBox="0 0 20 20"
                fill="currentColor"
                stroke="currentColor"
                strokeWidth="1"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
            </span>
          </label>
          <label htmlFor={`check-${id}`} className="text-xs capitalize">
            {id.replace(/([A-Z])/g, ' $1').trim()}
          </label>
        </div>
        <span
          {...attributes}
          {...listeners}
          className="icon icon-dots-six-vertical text-gray-400 py-1 rounded-md hover:bg-dark-500/5 cursor-move transition-base duration-200"
        />
      </div>
    </li>
  );
};

const Advertisements = () => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const displayMenuRef = useRef(null);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [filterText, setFilterText] = useState('');
  const statusDropdownRef = useRef(null);
  const [selectedTag, setSelectedTag] = useState('');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const categories = ['Furniture', 'Clothing'];
  const [products, setProducts] = useState([]);
  const [selectedStatus, setSelectedStatus] = useState('');

  async function getProducts() {
    const { payload } = await dispatch(getProductsAsync());
    setProducts(payload?.data);
  }

  const [displayProperties, setDisplayProperties] = useState({
    sku: true,
    status: true,
    variants: true,
    price: true,
    quantity: true,
  });
  const [items, setItems] = useState(Object.keys(displayProperties));

  // Add this new handler
  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);

        const newItems = [...items];
        newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, active.id);

        return newItems;
      });
    }
  };

  const data = [
    {
      id: 'P1004',
      name: 'Wireless Ergonomic Mouse',
      category: {
        name: 'Banner Ad',
      },
      status: 'Published',
      base_price: 49,
      clicks: 100,
    },

    {
      id: 'P1005',
      name: 'Desk Organizer',
      category: {
        name: 'Banner Ad',
      },
      status: 'Published',
      base_price: 29,
      clicks: 75,
    },
    {
      id: 'P1006',
      name: 'Portable Laptop Stand',
      category: {
        name: 'Banner Ad',
      },
      status: 'Published',
      base_price: 59,
      clicks: 40,
    },
    {
      id: 'P1007',
      name: 'Noise-Cancelling Headphones',
      category: {
        name: 'Promotion Ad',
      },
      status: 'Published',
      base_price: 199,
      clicks: 80,
    },
    // Add more sample data here
  ];

  return (
    <PrivateLayout>
      <div className="sm:flex bg-surface-100 rounded-xl ml-3 w-[calc(100% - 12px)] mt-[58x]">
        {/* Update the sidebar component to pass the state */}
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        {/* Update the content wrapper div className */}
        <div
          className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full p-6 mx-auto bg-surface-200 rounded-s-xl rounded-bl-xl overflow-auto h-[calc(100vh-60px)] transition-base`}
        >
          <div className="flex justify-between items-center mb-6">
            <div className="flex flex-col gap-1">
              <h1 className="text-xl font-bold">Advertisements</h1>
            </div>

            <div className="flex gap-1.5">
              <button className="flex items-center gap-2 px-4 py-1.5 text-sm text-nowrap font-semibold bg-gray-100 rounded-lg cursor-pointer hover:bg-gray-500/10 transition-base">
                Export
              </button>
              <Link
                href="/advertisements/create"
                className="btn custom-btn-shadow"
              >
                Create Advertisements
              </Link>
            </div>
          </div>

          <div className="rounded-xl">
            {/* Status tab filter */}
            {!isSearchFilterOpen && (
              <div className="flex items-center justify-between gap-2.5 bg-white p-2 rounded-tl-xl rounded-tr-xl border border-border-color ">
                <div className="flex items-center gap-2">
                  <button className="px-2.5 py-1.5 text-xs text-nowrap font-semibold rounded-xl cursor-pointer bg-dark-500/10 hover:bg-dark-500/10 transition-base">
                    All
                  </button>
                  <button className="px-2.5 py-1.5 text-xs text-nowrap font-semibold rounded-xl cursor-pointer text-gray-400 hover:bg-dark-500/10 transition-base">
                    Published
                  </button>
                  <button className="px-2.5 py-1.5 text-xs text-nowrap font-semibold rounded-xl cursor-pointer text-gray-400 hover:bg-dark-500/10 transition-base">
                    Draft
                  </button>
                  <button className="px-2.5 py-1.5 text-xs text-nowrap font-semibold rounded-xl cursor-pointer text-gray-400 hover:bg-dark-500/10 transition-base">
                    Archived
                  </button>
                </div>

                {/* Table or Grid view option */}
                <div className="flex items-center gap-2">
                  {/* Search & Filter */}
                  <button
                    onClick={() => setIsSearchFilterOpen(true)}
                    className="flex items-center gap-2 p-1.5 rounded-lg border border-border-color hover:bg-light-500/5 shadow-[0px_1px_0px_0px_#1A1A1A12] cursor-pointer transition-base"
                  >
                    <span className="icon icon-magnifying-glass text-dark-500 text-md" />
                    <span className="icon icon-sorting text-dark-500 text-md" />
                  </button>

                  <div className="relative" ref={displayMenuRef}>
                    <button
                      onClick={() => setIsDisplayMenuOpen(!isDisplayMenuOpen)}
                      className="flex items-center gap-2 p-1.5 rounded-lg border border-border-color hover:bg-light-500/5 shadow-[0px_1px_0px_0px_#1A1A1A12] cursor-pointer transition-base"
                    >
                      <span className="icon icon-square-split-horizontal text-dark-500 text-md" />
                      {/* <span className="text-xs">View</span> */}
                    </button>

                    {isDisplayMenuOpen && (
                      <div className="absolute top-full right-0 mt-1 w-[180px] bg-white rounded-xl shadow-custom p-2 z-20">
                        {/* <span className="text-sm px-3 py-2 font-bold inline-block">Display Properties</span> */}
                        <DndContext
                          collisionDetection={closestCenter}
                          onDragEnd={handleDragEnd}
                        >
                          <SortableContext
                            items={items}
                            strategy={verticalListSortingStrategy}
                          >
                            <ul className="space-y-1">
                              {items.map((key) => (
                                <SortableItem
                                  key={key}
                                  id={key}
                                  value={key}
                                  checked={displayProperties[key]}
                                  onChange={() =>
                                    setDisplayProperties((prev) => ({
                                      ...prev,
                                      [key]: !prev[key],
                                    }))
                                  }
                                />
                              ))}
                            </ul>
                          </SortableContext>
                        </DndContext>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Search and filter option */}
            {isSearchFilterOpen && (
              <div className="flex justify-between flex-col bg-white rounded-tl-xl rounded-tr-xl border border-border-color border-b-0">
                <div className="flex items-center justify-between gap-2 w-full border-b border-border-color p-2">
                  <InputField
                    type="text"
                    placeholder="Search products"
                    value={filterText}
                    marginBottom="mb-0 w-full"
                    leftIcon="icon-search"
                    onChange={(e) => setFilterText(e.target.value)}
                    inputClassName="w-full form-control !rounded-lg !min-h-[28px] !max-h-[28px] !py-1.5 !px-3"
                  />

                  <button
                    onClick={() => {
                      setIsSearchFilterOpen(false);
                      setFilterText('');
                    }}
                    className="rounded-lg min-h-[28px] font-medium py-1.5 px-3 bg-transparent text-gray-400 text-xs hover:text-danger-500 hover:bg-danger-500/10 transition-base cursor-pointer"
                  >
                    Cancel
                  </button>
                </div>

                <div className="p-2">
                  <div className="relative" ref={statusDropdownRef}>
                    <button
                      onClick={() => setIsFilterOpen(!isFilterOpen)}
                      className="flex items-center gap-2 px-3 py-1 min-h-[30px] rounded-lg border border-dashed border-border-color hover:bg-light-500/5 cursor-pointer transition-base"
                    >
                      {/* <span className="icon icon-funnel text-sm" /> */}
                      <span className="flex items-center gap-2 text-xs">
                        Categories
                        <span className="icon icon-caret-down" />
                      </span>
                      {selectedTag && (
                        <div className="flex items-center gap-1 px-2 py-0.5 text-dark-500 font-semibold bg-dark-500/10 rounded-2xl border-border-color hover:bg-danger-500/10 hover:text-danger-500 transition-base cursor-pointer">
                          <span className="text-xs">{selectedTag}</span>
                          <div
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedTag('');
                            }}
                            className="flex items-center justify-center cursor-pointer"
                          >
                            <span className="icon icon-x text-xs" />
                          </div>
                        </div>
                      )}
                    </button>

                    {isFilterOpen && (
                      <div className="absolute top-full left-0 w-[200px] mt-1 bg-white shadow-custom rounded-xl shadow-[0px_25px_60px_-10px_#1C273121] p-2 z-20">
                        <ul className="max-h-[280px] overflow-y-auto">
                          {categories.map((category) => (
                            <li
                              key={category}
                              className="px-2 py-1.5 hover:bg-primary-500/10 hover:text-primary-500 rounded-md cursor-pointer text-xs"
                              onClick={() => {
                                setSelectedTag(category);
                                setIsFilterOpen(false);
                              }}
                            >
                              {category}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Campaign Data */}
          {/* <CampaignsTable
            // data={products}
            data={data}
            filterText={filterText}
            selectedStatus={selectedStatus}
            displayProperties={displayProperties}
          /> */}
        </div>
      </div>
    </PrivateLayout>
  );
};

export default Advertisements;
