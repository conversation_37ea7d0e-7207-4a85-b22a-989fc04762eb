'use client';

import React, { useState, useRef } from 'react';
import DataTable from 'react-data-table-component';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import FilterField from '../../components/table/FilterField';
import SortableItem from '../../components/table/SortableItem';
import SortIcon from '../../components/table/SortIcon';
import TableSkeleton from '../../components/table/TableSkeleton';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';
import { Tooltip } from 'react-tooltip';
import CommonPagination from '../../components/table/CommonPagination';

const InfoRow = ({ label, value, className }) => (
  <div className={`flex flex-col gap-1 ${className}`}>
    <span className="text-sm text-gray-500/60 font-normal">{label}</span>
    <span className="text-sm text-dark-500 font-medium">{value}</span>
  </div>
);

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-border-color hover:border-border-color'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const InquiryDetailModal = ({ isOpen, onClose, inquiry }) => {
  if (!inquiry) return null;

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title={`Inquiry Details - ${inquiry.inquiryId}`}
      size="sm"
    >
      <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
        {/* Inquiry Information */}
        <div className="">
          <h4 className="font-semibold mb-3">Inquiry Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Inquiry ID" value={inquiry.inquiryId} />
            <InfoRow label="Inquiry Date" value={new Date(inquiry.inquiryDate).toLocaleDateString()} />
            <InfoRow label="Buyer Name" value={inquiry.buyerName} />
            <InfoRow label="Assigned To" value={inquiry.assignedTo || 'Unassigned'} />
          </div>
        </div>
        <hr/>
        {/* Contact Information */}
        <div className="">
          <h4 className="font-semibold mb-3">Contact Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Email" value={inquiry.buyerContactInfo.email} />
            <InfoRow label="Phone" value={inquiry.buyerContactInfo.phone} />
          </div>
        </div>
        <hr/>
        {/* Product Requirements */}
        <div className="">
          <h4 className="font-semibold mb-3">Product Requirements</h4>
          <div className="space-y-3">
            {inquiry.productsInterested.map((product, index) => (
              <div key={index} className="flex justify-between items-center p-3 border border-border-color rounded-lg">
                <div>
                  <p className="font-medium text-sm">{product.name}</p>
                  <p className="text-xs text-gray-300">SKU: {product.sku}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">{product.quantityRequired.toLocaleString()} units</p>
                  <p className="text-sm text-gray-300">Required</p>
                </div>
              </div>
            ))}
          </div>
        </div>
        <hr/>
        {/* Buyer Message */}
        <div className="">
          <h4 className="font-semibold mb-3">Buyer Message</h4>
          <p className="text-sm">{inquiry.buyerMessage}</p>
        </div>
        <hr/>
        {/* Preferred Timeline */}
        <div className="">
          <h4 className="font-semibold mb-3">Preferred Timeline</h4>
          <p className="text-sm">{inquiry.preferredTimeline}</p>
        </div>
        {/* Comments History */}
        {inquiry.commentsHistory && inquiry.commentsHistory.length > 0 && (
          <>
            <hr/>
            <div className="">
              <h4 className="font-semibold mb-3">Comments History</h4>
              <div className="space-y-3">
                {inquiry.commentsHistory.map((comment, index) => (
                  <div key={index} className="p-3 border border-border-color rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <span className="font-medium text-sm">{comment.author}</span>
                      <span className="text-xs text-gray-500">{new Date(comment.date).toLocaleDateString()}</span>
                    </div>
                    <p className="text-sm">{comment.message}</p>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

      </div>
      {/* Action Buttons */}
      <div className="flex justify-end gap-3 border-t border-border-color p-4">
        <button className="btn btn-primary">Convert to Order</button>
        <button className="btn btn-gray">Assign to Rep</button>
        <button className="btn btn-outline-danger">Reject Inquiry</button>
      </div>
    </BaseOffCanvas>
  );
};

const BulkOrderInquiries = ({ isLoading = false }) => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedInquiry, setSelectedInquiry] = useState(null);
  const [isInquiryDetailOpen, setIsInquiryDetailOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const displayMenuRef = useRef(null);

  const [displayProperties, setDisplayProperties] = useState({
    inquiryId: true,
    inquiryDate: true,
    buyerName: true,
    productsInterested: true,
    quantityRequired: true,
    buyerContactInfo: true,
    status: true,
    assignedTo: true,
  });

  const [items, setItems] = useState(Object.keys(displayProperties));

  // Sample data - in real app this would come from API
  const [inquiries] = useState([
    {
      id: 'INQ-001',
      inquiryId: 'INQ-001',
      inquiryDate: '2024-01-16T10:30:00Z',
      buyerName: 'Global Tech Solutions',
      productsInterested: [
        { name: 'Industrial Servers', sku: 'IS-001', quantityRequired: 25 },
        { name: 'Network Switches', sku: 'NS-002', quantityRequired: 50 }
      ],
      quantityRequired: 75,
      buyerContactInfo: {
        email: '<EMAIL>',
        phone: '******-0199'
      },
      status: 'New',
      assignedTo: null,
      buyerMessage: 'We are looking for high-quality industrial servers and network switches for our new data center. Please provide competitive pricing for bulk quantities.',
      preferredTimeline: 'Within 30 days',
      commentsHistory: [
        {
          author: 'Global Tech Solutions',
          date: '2024-01-16T10:30:00Z',
          message: 'Initial inquiry for bulk purchase.'
        },
        {
          author: 'John Smith',
          date: '2024-01-16T12:00:00Z',
          message: 'Sent initial quote for review.'
        }
      ]
    },
    {
      id: 'INQ-002',
      inquiryId: 'INQ-002',
      inquiryDate: '2024-01-15T14:20:00Z',
      buyerName: 'Manufacturing Plus Inc',
      productsInterested: [
        { name: 'Steel Rods', sku: 'SR-003', quantityRequired: 1000 }
      ],
      quantityRequired: 1000,
      buyerContactInfo: {
        email: '<EMAIL>',
        phone: '******-0188'
      },
      status: 'Under Review',
      assignedTo: 'John Smith',
      buyerMessage: 'Need steel rods for construction project. Quality certification required.',
      preferredTimeline: 'ASAP - within 2 weeks',
      commentsHistory: [
        {
          author: 'John Smith',
          date: '2024-01-15T16:00:00Z',
          message: 'Contacted supplier for pricing and availability.'
        }
      ]
    },
    {
      id: 'INQ-003',
      inquiryId: 'INQ-003',
      inquiryDate: '2024-01-14T09:15:00Z',
      buyerName: 'Office Supplies Co',
      productsInterested: [
        { name: 'Office Chairs', sku: 'OC-004', quantityRequired: 200 },
        { name: 'Desks', sku: 'DK-005', quantityRequired: 100 }
      ],
      quantityRequired: 300,
      buyerContactInfo: {
        email: '<EMAIL>',
        phone: '******-0177'
      },
      status: 'Converted',
      assignedTo: 'Sarah Johnson',
      buyerMessage: 'Looking for ergonomic office furniture for new office setup.',
      preferredTimeline: 'Flexible - within 45 days',
      commentsHistory: [
        {
          author: 'Sarah Johnson',
          date: '2024-01-14T11:00:00Z',
          message: 'Inquiry converted to order BO-005.'
        }
      ]
    },
    {
      id: 'INQ-004',
      inquiryId: 'INQ-004',
      inquiryDate: '2024-01-13T16:45:00Z',
      buyerName: 'Retail Chain Ltd',
      productsInterested: [
        { name: 'Display Units', sku: 'DU-006', quantityRequired: 150 }
      ],
      quantityRequired: 150,
      buyerContactInfo: {
        email: '<EMAIL>',
        phone: '******-0166'
      },
      status: 'Rejected',
      assignedTo: 'Mike Davis',
      buyerMessage: 'Need custom display units for retail stores.',
      preferredTimeline: 'Within 60 days',
      commentsHistory: [
        {
          author: 'Mike Davis',
          date: '2024-01-13T18:00:00Z',
          message: 'Rejected due to custom requirements outside our capabilities.'
        }
      ]
    }
  ]);

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'new':
        return 'bg-info-500/20 text-info-500';
      case 'under review':
        return 'bg-yellow-100 text-yellow-800';
      case 'converted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns = [
    {
      name: 'Inquiry ID',
      selector: (row) => row.inquiryId,
      sortable: true,
      omit: !displayProperties.inquiryId,
    },
    {
      name: 'Inquiry Date',
      selector: (row) => row.inquiryDate,
      sortable: true,
      omit: !displayProperties.inquiryDate,
      cell: (row) => new Date(row.inquiryDate).toLocaleDateString(),
    },
    {
      name: 'Buyer Name',
      selector: (row) => row.buyerName,
      sortable: true,
      omit: !displayProperties.buyerName,
    },
    {
      name: 'Products Interested',
      selector: (row) => row.productsInterested,
      omit: !displayProperties.productsInterested,
      cell: (row) => (
        <div className="text-sm">
          {row.productsInterested.length === 1 ? (
            <div>{row.productsInterested[0].name}</div>
          ) : (
            <div>{row.productsInterested.length} products</div>
          )}
        </div>
      ),
    },
    {
      name: 'Quantity Required',
      selector: (row) => row.quantityRequired,
      sortable: true,
      omit: !displayProperties.quantityRequired,
      cell: (row) => `${row.quantityRequired.toLocaleString()} units`,
    },
    {
      name: 'Buyer Contact Info',
      selector: (row) => row.buyerContactInfo,
      omit: !displayProperties.buyerContactInfo,
      width: '190px',
      cell: (row) => (
        <div className="text-sm">
          <span className="text-gray-400 text-xs">{row.buyerContactInfo.email}</span>
          <div className="text-xs text-gray-400">{row.buyerContactInfo.phone}</div>
        </div>
      ),
    },
    {
      name: 'Status',
      selector: (row) => row.status,
      sortable: true,
      omit: !displayProperties.status,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(row.status)}`}>
          {row.status}
        </span>
      ),
    },
    {
      name: 'Assigned To',
      selector: (row) => row.assignedTo,
      sortable: true,
      omit: !displayProperties.assignedTo,
      cell: (row) => row.assignedTo || '-',
    },
    {
      name: 'Actions',
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewInquiry(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Details"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            {/* View Details */}
            <span className="icon icon-eye text-base" />
          </button>
          {row.status === 'New' && (
            <>
              <button 
                data-tooltip-id="convert-tooltip"
                data-tooltip-content="Convert to Order"
                className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-success-500/10 hover:text-success-500 rounded-lg cursor-pointer transition-base">
                {/* Convert to Order */}
                <span className="icon icon-check-3 text-base" />
              </button>
              <button 
                data-tooltip-id="reject-tooltip"
                data-tooltip-content="Reject"
                className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base">
                {/* Reject */}
                <span className="icon icon-x text-base" />
              </button>
            </>
          )}
          {row.status === 'Under Review' && (
            <button 
              data-tooltip-id="update-status-tooltip"
              data-tooltip-content="Update Status"
              className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-primary-500/10 hover:text-primary-500 rounded-lg cursor-pointer transition-base"
            >
              {/* Update Status */}
              <span className="icon icon-pencil-line text-base" />
            </button>
          )}

          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="convert-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="reject-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="update-status-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      // allowOverflow: true,
      // button: true,
      width: '150px',
    },
  ];

  const handleViewInquiry = (inquiry) => {
    setSelectedInquiry(inquiry);
    setIsInquiryDetailOpen(true);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);
        const newItems = [...items];
        newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, active.id);
        return newItems;
      });
    }
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  const filteredInquiries = inquiries.filter((inquiry) =>
    inquiry.buyerName.toLowerCase().includes(filterText.toLowerCase()) ||
    inquiry.inquiryId.toLowerCase().includes(filterText.toLowerCase()) ||
    inquiry.buyerContactInfo.email.toLowerCase().includes(filterText.toLowerCase())
  );

  return (
    <div className="space-y-4">
      <div className="rounded-xl">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
        >
          <div className="absolute top-full right-0 mt-1 w-[200px] bg-white rounded-xl shadow-custom p-2 z-20">
            <DndContext
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={items}
                strategy={verticalListSortingStrategy}
              >
                <ul className="space-y-1">
                  {items.map((key) => (
                    <SortableItem
                      key={key}
                      id={key}
                      value={key}
                      checked={displayProperties[key]}
                      onChange={() =>
                        setDisplayProperties((prev) => ({
                          ...prev,
                          [key]: !prev[key],
                        }))
                      }
                    />
                  ))}
                </ul>
              </SortableContext>
            </DndContext>
          </div>
        </FilterField>

        {isLoading ? (
          <TableSkeleton 
            rowsPerPage={8}
            columns={[
              { size: 'small', grow: 1 },
              { size: 'small' },
              { size: 'medium', grow: 1 },
              { size: 'medium' },
              { size: 'small' },
              { size: 'medium' },
              { size: 'small' },
              { size: 'small' },
              { size: 'large' },
            ]}
            showCheckbox={true}
            cellHeight={4}
          />
        ) : (
          <DataTable
            columns={columns}
            data={filteredInquiries}
            customStyles={customStyles}
            pagination
            selectableRows
            fixedHeader={true}
            selectableRowsHighlight
            selectableRowsComponent={CustomCheckbox}
            onSelectedRowsChange={handleSelectedRowsChange}
            selectableRowsComponentProps={{ 'aria-label': 'Select Row' }}
            className="custom-table auto-height-table"
            sortIcon={<SortIcon sortDirection={sortDirection} />}
            onSort={handleSort}
            sortField={sortedField}
            defaultSortAsc={true}
            paginationPerPage={8}
            paginationRowsPerPageOptions={[8]}
            paginationComponentOptions={{
              rowsPerPageText: 'Rows per page:',
              rangeSeparatorText: 'of',
              selectAllRowsItem: false,
              noRowsPerPage: true,
            }}
            paginationComponent={(props) => (
              <CommonPagination
                selectedCount={props.selectedRows?.length}
                total={props.totalRows}
                page={props.currentPage}
                perPage={props.rowsPerPage}
                onPageChange={props.onChangePage}
              />
            )}
          />
        )}
      </div>

      {/* Inquiry Detail Modal */}
      <InquiryDetailModal
        isOpen={isInquiryDetailOpen}
        onClose={() => setIsInquiryDetailOpen(false)}
        inquiry={selectedInquiry}
      />
    </div>
  );
};

export default BulkOrderInquiries;
