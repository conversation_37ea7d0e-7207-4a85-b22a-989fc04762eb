'use client';

import React, { useState, useRef } from 'react';
import DataTable from 'react-data-table-component';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import FilterField from '../../components/table/FilterField';
import SortableItem from '../../components/table/SortableItem';
import SortIcon from '../../components/table/SortIcon';
import TableSkeleton from '../../components/table/TableSkeleton';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';
import { Tooltip } from 'react-tooltip';
import CommonPagination from '../../components/table/CommonPagination';

// InfoRow components
const InfoRow = ({ label, value, className, valueClassName }) => (
  <div className={`flex flex-col gap-1 ${className}`}>
    <span className="text-sm text-gray-500/60 font-normal">{label}</span>
    <span className={`text-sm text-dark-500 font-medium ${valueClassName}`}>{value}</span>
  </div>
);

// Custom checkbox component
const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

// NegotiationDetailModal component
const NegotiationDetailModal = ({ isOpen, onClose, negotiation }) => {
  if (!negotiation) return null;

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title={`Negotiation Details - ${negotiation.negotiationId}`}
      size="sm"
    >
      <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
        {/* Negotiation Information */}
        <div className="">
          <h4 className="font-semibold mb-3">Negotiation Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Negotiation ID" value={negotiation.negotiationId} />
            <InfoRow label="Related Order/Inquiry" value={negotiation.relatedOrderInquiry} />
            <InfoRow label="Buyer Name" value={negotiation.buyerName} />
            <InfoRow label="Date Finalized" value={negotiation.dateFinalized ? new Date(negotiation.dateFinalized).toLocaleDateString() : 'Pending'} />
          </div>
        </div>

        <hr/>

        {/* Negotiation Information */}
        <div className="">
          <h4 className="font-semibold mb-3">Negotiation Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Negotiation ID" value={negotiation.negotiationId} />
            <InfoRow label="Related Order/Inquiry" value={negotiation.relatedOrderInquiry} />
            <InfoRow label="Buyer Name" value={negotiation.buyerName} />
            <InfoRow label="Date Finalized" value={negotiation.dateFinalized ? new Date(negotiation.dateFinalized).toLocaleDateString() : 'Pending'} />
          </div>
        </div>

        <hr/>

        {/* Pricing Information */}
        <div className="">
          <h4 className="font-semibold mb-3">Pricing Information</h4>
          <div className="grid grid-cols-3 gap-4">
            <InfoRow label="Original Quote" value={`$${negotiation.originalQuote.toLocaleString()}`} />
            <InfoRow label="Proposed Price" value={`$${negotiation.proposedPrice.toLocaleString()}`} />
            <InfoRow label="Final Approved Price" value={negotiation.finalApprovedPrice ? `$${negotiation.finalApprovedPrice.toLocaleString()}` : 'Pending'} />
          </div>
        </div>

        <hr/>

        {/* Approval Information */}
        <div className="">
          <h4 className="font-semibold mb-3">Approval Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Negotiation Status" value={negotiation.negotiationStatus} valueClassName={negotiation.negotiationStatus === 'Approved' ? '!text-success-500' : '!text-danger-500'} />
            <InfoRow label="Approved By" value={negotiation.approvedBy || 'Pending'} />
          </div>
        </div>
        <hr/>

        {/* Message Thread */}
        <div className="">
          <h4 className="font-semibold mb-3">Message Thread</h4>
          <div className="space-y-3 max-h-60 overflow-y-auto">
            {negotiation.messageThread.map((message, index) => (
              <div key={index} className="flex flex-col p-3 border border-border-color rounded-lg">
                <div className="flex justify-between items-start mb-1">
                  <span className="font-medium text-sm">{message.sender}</span>
                  <span className="text-xs text-gray-300">{new Date(message.timestamp).toLocaleString()}</span>
                </div>
                <p className="text-sm text-gray-400">{message.message}</p>
                {message.priceOffer && (
                  <div className="mt-2 self-start">
                    <span className="text-sm font-semibold text-blue-800">Price Offer: ${message.priceOffer.toLocaleString()}</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* File Attachments */}
        {negotiation.fileAttachments && negotiation.fileAttachments.length > 0 && (
          <div className="">
            <h4 className="font-semibold mb-3">File Attachments</h4>
            <div className="space-y-2">
              {negotiation.fileAttachments.map((file, index) => (
                <div key={index} className="flex items-center gap-1 justify-between p-3 border border-border-color rounded-lg">
                  <div className="flex items-center gap-1">
                    <span className="icon icon-file-pdf text-dark-500"></span>
                    <span className="text-sm">{file.name}</span>
                  </div>
                  <button className="text-primary-500 hover:text-primary-600 text-sm">
                    Download
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

      </div>
      {/* Action Buttons */}
      <div className="flex justify-end gap-3 border-t border-border-color p-4">
        {negotiation.negotiationStatus === 'In Discussion' && (
          <>
            <button className="btn btn-primary">Finalize Negotiation</button>
            <button className="btn btn-outline-gray">Add Comment</button>
          </>
        )}
        {negotiation.negotiationStatus === 'Declined' && (
          <button className="btn btn-outline-gray">Reopen Negotiation</button>
        )}
        <button className="btn btn-outline-gray">View Thread</button>
      </div>
    </BaseOffCanvas>
  );
};

const PriceNegotiationTracker = ({ isLoading = false }) => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedNegotiation, setSelectedNegotiation] = useState(null);
  const [isNegotiationDetailOpen, setIsNegotiationDetailOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const displayMenuRef = useRef(null);

  const [displayProperties, setDisplayProperties] = useState({
    negotiationId: true,
    relatedOrderInquiry: true,
    buyerName: true,
    originalQuote: true,
    proposedPrice: true,
    finalApprovedPrice: true,
    negotiationStatus: true,
    approvedBy: true,
    dateFinalized: true,
  });

  const [items, setItems] = useState(Object.keys(displayProperties));

  // Sample data - in real app this would come from API
  const [negotiations] = useState([
    {
      id: 'NEG-001',
      negotiationId: 'NEG-001',
      relatedOrderInquiry: 'INQ-001',
      buyerName: 'Global Tech Solutions',
      originalQuote: 125000,
      proposedPrice: 115000,
      finalApprovedPrice: 120000,
      negotiationStatus: 'Approved',
      approvedBy: 'Manager Smith',
      dateFinalized: '2024-01-16T15:30:00Z',
      messageThread: [
        {
          sender: 'Global Tech Solutions',
          timestamp: '2024-01-15T10:00:00Z',
          message: 'We would like to negotiate the price for the bulk order.',
          priceOffer: 115000
        },
        {
          sender: 'Sales Rep',
          timestamp: '2024-01-15T14:00:00Z',
          message: 'We can offer a 4% discount for this quantity.',
          priceOffer: 120000
        },
        {
          sender: 'Global Tech Solutions',
          timestamp: '2024-01-16T09:00:00Z',
          message: 'Agreed. Please proceed with the order.',
          priceOffer: null
        }
      ],
      fileAttachments: [
        { name: 'quote_comparison.pdf', size: '245KB' }
      ]
    },
    {
      id: 'NEG-002',
      negotiationId: 'NEG-002',
      relatedOrderInquiry: 'BO-002',
      buyerName: 'Manufacturing Plus Inc',
      originalQuote: 30000,
      proposedPrice: 27000,
      finalApprovedPrice: null,
      negotiationStatus: 'In Discussion',
      approvedBy: null,
      dateFinalized: null,
      messageThread: [
        {
          sender: 'Manufacturing Plus Inc',
          timestamp: '2024-01-14T11:00:00Z',
          message: 'Can we get a better price for this bulk order?',
          priceOffer: 27000
        },
        {
          sender: 'Sales Rep',
          timestamp: '2024-01-14T16:00:00Z',
          message: 'Let me check with management and get back to you.',
          priceOffer: null
        }
      ],
      fileAttachments: []
    },
    {
      id: 'NEG-003',
      negotiationId: 'NEG-003',
      relatedOrderInquiry: 'INQ-003',
      buyerName: 'Office Supplies Co',
      originalQuote: 45000,
      proposedPrice: 40000,
      finalApprovedPrice: null,
      negotiationStatus: 'Declined',
      approvedBy: 'Manager Johnson',
      dateFinalized: '2024-01-13T12:00:00Z',
      messageThread: [
        {
          sender: 'Office Supplies Co',
          timestamp: '2024-01-12T14:00:00Z',
          message: 'We need a significant discount to proceed.',
          priceOffer: 40000
        },
        {
          sender: 'Sales Rep',
          timestamp: '2024-01-13T10:00:00Z',
          message: 'Unfortunately, we cannot meet that price point.',
          priceOffer: null
        }
      ],
      fileAttachments: []
    }
  ]);

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'in discussion':
        return 'bg-yellow-100 text-yellow-800';
      case 'declined':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns = [
    {
      name: 'Negotiation ID',
      selector: (row) => row.negotiationId,
      sortable: true,
      omit: !displayProperties.negotiationId,
    },
    {
      name: 'Related Order/Inquiry',
      selector: (row) => row.relatedOrderInquiry,
      sortable: true,
      omit: !displayProperties.relatedOrderInquiry,
    },
    {
      name: 'Buyer Name',
      selector: (row) => row.buyerName,
      sortable: true,
      omit: !displayProperties.buyerName,
    },
    {
      name: 'Original Quote',
      selector: (row) => row.originalQuote,
      sortable: true,
      omit: !displayProperties.originalQuote,
      cell: (row) => `$${row.originalQuote.toLocaleString()}`,
    },
    {
      name: 'Proposed Price',
      selector: (row) => row.proposedPrice,
      sortable: true,
      omit: !displayProperties.proposedPrice,
      cell: (row) => `$${row.proposedPrice.toLocaleString()}`,
    },
    {
      name: 'Final Approved Price',
      selector: (row) => row.finalApprovedPrice,
      sortable: true,
      omit: !displayProperties.finalApprovedPrice,
      cell: (row) => row.finalApprovedPrice ? `$${row.finalApprovedPrice.toLocaleString()}` : '-',
    },
    {
      name: 'Negotiation Status',
      selector: (row) => row.negotiationStatus,
      sortable: true,
      omit: !displayProperties.negotiationStatus,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(row.negotiationStatus)}`}>
          {row.negotiationStatus}
        </span>
      ),
    },
    {
      name: 'Approved By',
      selector: (row) => row.approvedBy,
      sortable: true,
      omit: !displayProperties.approvedBy,
      cell: (row) => row.approvedBy || '-',
    },
    {
      name: 'Date Finalized',
      selector: (row) => row.dateFinalized,
      sortable: true,
      omit: !displayProperties.dateFinalized,
      cell: (row) => row.dateFinalized ? new Date(row.dateFinalized).toLocaleDateString() : '-',
    },
    {
      name: 'Actions',
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewNegotiation(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Thread"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            {/* View Thread */}
            <span className="icon icon-eye text-base" />
          </button>
          {row.negotiationStatus === 'In Discussion' && (
            <>
              <button 
                data-tooltip-id="finalize-tooltip"
                data-tooltip-content="Finalize"
                className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-success-500/10 hover:text-success-500 rounded-lg cursor-pointer transition-base">
                {/* Finalize */}
                <span className="icon icon-check-3 text-base" />
              </button>
              <button 
                data-tooltip-id="update-tooltip"
                data-tooltip-content="Update"
                className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-primary-500/10 hover:text-primary-500 rounded-lg cursor-pointer transition-base">
                {/* Update */}
                <span className="icon icon-pencil-line text-base" />
              </button>
            </>
          )}
          {row.negotiationStatus === 'Declined' && (
            <button 
              data-tooltip-id="reopen-tooltip"
              data-tooltip-content="Reopen"
              className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-gray-500/10 hover:text-gray-500 rounded-lg cursor-pointer transition-base">
              {/* Reopen */}
              <span className="icon icon-return text-base" />
            </button>
          )}

          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="finalize-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="update-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="reopen-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      // allowOverflow: true,
      // button: true,
      width: '150px',
    },
  ];

  const handleViewNegotiation = (negotiation) => {
    setSelectedNegotiation(negotiation);
    setIsNegotiationDetailOpen(true);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);
        const newItems = [...items];
        newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, active.id);
        return newItems;
      });
    }
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  const filteredNegotiations = negotiations.filter((negotiation) =>
    negotiation.buyerName.toLowerCase().includes(filterText.toLowerCase()) ||
    negotiation.negotiationId.toLowerCase().includes(filterText.toLowerCase()) ||
    negotiation.relatedOrderInquiry.toLowerCase().includes(filterText.toLowerCase())
  );

  return (
    <div className="space-y-4">
      <div className="rounded-xl">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
        >
          <div className="absolute top-full right-0 mt-1 w-[200px] bg-white rounded-xl shadow-custom p-2 z-20">
            <DndContext
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={items}
                strategy={verticalListSortingStrategy}
              >
                <ul className="space-y-1">
                  {items.map((key) => (
                    <SortableItem
                      key={key}
                      id={key}
                      value={key}
                      checked={displayProperties[key]}
                      onChange={() =>
                        setDisplayProperties((prev) => ({
                          ...prev,
                          [key]: !prev[key],
                        }))
                      }
                    />
                  ))}
                </ul>
              </SortableContext>
            </DndContext>
          </div>
        </FilterField>

        {isLoading ? (
          <TableSkeleton 
            rowsPerPage={8}
            columns={[
              { size: 'small', grow: 1 },
              { size: 'small' },
              { size: 'medium', grow: 1 },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'large' },
            ]}
            showCheckbox={true}
            cellHeight={4}
          />
        ) : (
          <DataTable
            columns={columns}
            data={filteredNegotiations}
            customStyles={customStyles}
            pagination
            selectableRows
            fixedHeader={true}
            selectableRowsHighlight
            selectableRowsComponent={CustomCheckbox}
            onSelectedRowsChange={handleSelectedRowsChange}
            selectableRowsComponentProps={{ 'aria-label': 'Select Row' }}
            className="custom-table auto-height-table"
            sortIcon={<SortIcon sortDirection={sortDirection} />}
            onSort={handleSort}
            sortField={sortedField}
            defaultSortAsc={true}
            paginationPerPage={8}
            paginationRowsPerPageOptions={[8]}
            paginationComponentOptions={{
              rowsPerPageText: 'Rows per page:',
              rangeSeparatorText: 'of',
              selectAllRowsItem: false,
              noRowsPerPage: true,
            }}
            paginationComponent={(props) => (
              <CommonPagination
                selectedCount={props.selectedRows?.length}
                total={props.totalRows}
                page={props.currentPage}
                perPage={props.rowsPerPage}
                onPageChange={props.onChangePage}
              />
            )}
          />
        )}
      </div>

      {/* Negotiation Detail Modal */}
      <NegotiationDetailModal
        isOpen={isNegotiationDetailOpen}
        onClose={() => setIsNegotiationDetailOpen(false)}
        negotiation={selectedNegotiation}
      />
    </div>
  );
};

export default PriceNegotiationTracker;
