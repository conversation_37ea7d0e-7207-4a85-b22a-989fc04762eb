'use client';

import React, { useState } from 'react';
import PrivateLayout from '../components/layout/PrivateLayout';
import Sidebar from '../components/sidebar/Sidebar';
import Breadcrumb from '../components/Inputs/Breadcrumb';
import PurchaseRequests from './components/PurchaseRequests';
import VendorQuotations from './components/VendorQuotations';
import PurchaseOrderApproval from './components/PurchaseOrderApproval';
import ProcurementReturns from './components/ProcurementReturns';

export default function Procurement() {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [activeTab, setActiveTab] = useState('purchase-requests');
  const [isLoading] = useState(false);

  const tabs = [
    { id: 'purchase-requests', label: 'Purchase Requests', icon: 'icon-file-plus' },
    { id: 'vendor-quotations', label: 'Vendor Quotations', icon: 'icon-file-text' },
    { id: 'purchase-orders', label: 'Purchase Order Approval', icon: 'icon-check-circle' },
    { id: 'returns', label: 'Procurement Returns', icon: 'icon-refresh-ccw' }
  ];

  const breadcrumbItems = [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Procurement', href: '/procurement' }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'purchase-requests':
        return <PurchaseRequests isLoading={isLoading} />;
      case 'vendor-quotations':
        return <VendorQuotations isLoading={isLoading} />;
      case 'purchase-orders':
        return <PurchaseOrderApproval isLoading={isLoading} />;
      case 'returns':
        return <ProcurementReturns isLoading={isLoading} />;
      default:
        return <PurchaseRequests isLoading={isLoading} />;
    }
  };

  return (
    <PrivateLayout>
      <div className="flex rounded-xl mt-[60px] w-full">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        <div
          className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full mx-auto py-6 px-8 bg-surface-100 overflow-auto h-[calc(100dvh-60px)] transition-base`}
        >
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <div className="flex flex-col gap-1">
              <h1 className="text-xl font-bold">Procurement</h1>
              <Breadcrumb items={breadcrumbItems} />
            </div>
            <div className="flex gap-3">
              <button className="btn btn-outline-gray flex items-center gap-2">
                <span className="icon icon-upload-simple text-base" />
                Export
              </button>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="flex items-center gap-1 mb-4 bg-white p-1 rounded-xl border border-border-color w-fit">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-base ${
                  activeTab === tab.id
                    ? 'bg-primary-500 text-white'
                    : 'text-gray-400 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div className="flex-1">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
}
