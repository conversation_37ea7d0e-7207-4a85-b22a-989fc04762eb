'use client';

import React, { useState, useRef } from 'react';
import DataTable from 'react-data-table-component';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import FilterField from '../../components/table/FilterField';
import SortableItem from '../../components/table/SortableItem';
import SortIcon from '../../components/table/SortIcon';
import TableSkeleton from '../../components/table/TableSkeleton';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';
import { Tooltip } from 'react-tooltip';
import CommonPagination from '../../components/table/CommonPagination';

// Info row component
const InfoRow = ({ label, value, className }) => (
  <div className={`flex flex-col gap-1 ${className}`}>
    <span className="text-sm text-gray-500/60 font-normal">{label}</span>
    <span className="text-sm text-dark-500 font-medium">{value}</span>
  </div>
);

// Custom checkbox component
const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

// Request detail modal
const RequestDetailModal = ({ isOpen, onClose, request }) => {
  if (!request) return null;

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title={`Purchase Request - ${request.requestId}`}
      size="md"
    >
      <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
        {/* Request Information */}
        <div className="">
          <h4 className="font-semibold mb-3">Request Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Request ID" value={request.requestId} />
            <InfoRow label="Request Date" value={new Date(request.requestDate).toLocaleDateString()} />
            <InfoRow label="Requested By" value={request.requestedBy} />
            <InfoRow label="Department" value={request.department} />
            {/* <InfoRow label="Priority" value={request.priority} /> */}
            <div className='flex flex-col gap-1 items-start'>
              <span className="text-sm text-gray-500/60 font-normal">Priority</span>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                request.priority === 'High' ? 'bg-red-100 text-red-800' : 
                request.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' : 
                'bg-green-100 text-green-800'
              }`}>
                {request.priority}
              </span>
            </div>
            <InfoRow label="Required By Date" value={new Date(request.requiredByDate).toLocaleDateString()} />
          </div>
        </div>
        <hr/>

        {/* Products Requested */}
        <div className="">
          <h4 className="font-semibold mb-3">Products Requested</h4>
          <div className="space-y-3">
            {request.products.map((product, index) => (
              <div key={index} className="flex justify-between items-center p-3 border border-border-color rounded-lg">
                <div>
                  <p className="text-sm font-medium">{product.name}</p>
                  <p className="text-xs text-gray-400">SKU: {product.sku}</p>
                  <p className="text-xs text-gray-400">Specification: {product.specification}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">{product.quantity.toLocaleString()} {product.unit}</p>
                  <p className="text-xs text-dark-500 font-medium">Est. Cost: ${product.estimatedCost.toFixed(2)}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
        <hr/>

        {/* Justification & Notes */}
        <div className="">
          <h4 className="font-semibold mb-3">Justification & Notes</h4>
          <p className="text-sm">{request.notesJustification}</p>
        </div>
        <hr/>

        {/* Preferred Vendors */}
        {request.preferredVendors && request.preferredVendors.length > 0 && (
          <div className="">
            <h4 className="font-semibold mb-3">Preferred Vendors</h4>
            <div className="flex flex-wrap gap-2">
              {request.preferredVendors.map((vendor, index) => (
                <span key={index} className="inline-flex px-2 py-1 text-xs font-medium bg-primary-500/10 text-primary-500 rounded-md">
                  {vendor}
                </span>
              ))}
            </div>
          </div>
        )}
        <hr/>
        {/* Cost Center */}
        {request.costCenter && (
          <div className="">
            <h4 className="font-semibold mb-3">Cost Center</h4>
            <p className="text-sm">{request.costCenter}</p>
          </div>
        )}

      </div>
      {/* Action Buttons */}
      <div className="flex justify-end gap-3 border-t border-border-color p-4">
        {request.requestStatus === 'Pending' && (
          <>
            <button className="btn btn-primary">Approve Request</button>
            <button className="btn btn-gray">Request More Info</button>
            <button className="btn btn-outline-danger">Cancel Request</button>
          </>
        )}
        {request.requestStatus === 'Reviewed' && (
          <button className="btn btn-primary">Convert to PO</button>
        )}
        <button className="btn btn-outline-gray">Edit Request</button>
      </div>
    </BaseOffCanvas>
  );
};

const PurchaseRequests = ({ isLoading = false }) => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [isRequestDetailOpen, setIsRequestDetailOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const displayMenuRef = useRef(null);

  const [displayProperties, setDisplayProperties] = useState({
    requestId: true,
    requestDate: true,
    requestedBy: true,
    products: true,
    quantity: true,
    priority: true,
    requiredByDate: true,
    requestStatus: true,
    notesJustification: true,
  });

  const [items, setItems] = useState(Object.keys(displayProperties));

  // Sample data - in real app this would come from API
  const [requests] = useState([
    {
      id: 'PR-001',
      requestId: 'PR-001',
      requestDate: '2024-01-20T09:00:00Z',
      requestedBy: 'John Smith',
      department: 'IT Department',
      products: [
        { name: 'Laptops', sku: 'LP-001', quantity: 10, unit: 'units', specification: 'Intel i7, 16GB RAM, 512GB SSD', estimatedCost: 1200.00 },
        { name: 'Monitors', sku: 'MN-001', quantity: 10, unit: 'units', specification: '24-inch, 1080p, IPS', estimatedCost: 300.00 }
      ],
      quantity: 20,
      priority: 'High',
      requiredByDate: '2024-02-01T00:00:00Z',
      requestStatus: 'Pending',
      notesJustification: 'New employee onboarding requires additional workstations for the development team.',
      preferredVendors: ['TechCorp', 'CompuWorld'],
      costCenter: 'IT-2024-Q1'
    },
    {
      id: 'PR-002',
      requestId: 'PR-002',
      requestDate: '2024-01-19T14:30:00Z',
      requestedBy: 'Sarah Johnson',
      department: 'Operations',
      products: [
        { name: 'Office Chairs', sku: 'OC-002', quantity: 25, unit: 'units', specification: 'Ergonomic, adjustable height', estimatedCost: 250.00 }
      ],
      quantity: 25,
      priority: 'Medium',
      requiredByDate: '2024-02-15T00:00:00Z',
      requestStatus: 'Reviewed',
      notesJustification: 'Replacement of old chairs in the main office area to improve employee comfort.',
      preferredVendors: ['OfficePlus'],
      costCenter: 'OPS-2024-Q1'
    },
    {
      id: 'PR-003',
      requestId: 'PR-003',
      requestDate: '2024-01-18T11:15:00Z',
      requestedBy: 'Mike Davis',
      department: 'Facilities',
      products: [
        { name: 'Cleaning Supplies', sku: 'CS-003', quantity: 100, unit: 'units', specification: 'Eco-friendly cleaning products', estimatedCost: 15.00 }
      ],
      quantity: 100,
      priority: 'Low',
      requiredByDate: '2024-02-28T00:00:00Z',
      requestStatus: 'Converted',
      notesJustification: 'Monthly restocking of cleaning supplies for facility maintenance.',
      preferredVendors: ['CleanCorp'],
      costCenter: 'FAC-2024-Q1'
    },
    {
      id: 'PR-004',
      requestId: 'PR-004',
      requestDate: '2024-01-17T16:45:00Z',
      requestedBy: 'Emily Wilson',
      department: 'Marketing',
      products: [
        { name: 'Promotional Materials', sku: 'PM-004', quantity: 500, unit: 'pieces', specification: 'Branded pens, notebooks, stickers', estimatedCost: 5.00 }
      ],
      quantity: 500,
      priority: 'Medium',
      requiredByDate: '2024-02-10T00:00:00Z',
      requestStatus: 'Cancelled',
      notesJustification: 'Marketing materials for upcoming trade show and client meetings.',
      preferredVendors: ['PrintPro'],
      costCenter: 'MKT-2024-Q1'
    }
  ]);

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'reviewed':
        return 'bg-info-500/20 text-info-500';
      case 'converted':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns = [
    {
      name: 'Request ID',
      selector: (row) => row.requestId,
      sortable: true,
      omit: !displayProperties.requestId,
    },
    {
      name: 'Request Date',
      selector: (row) => row.requestDate,
      sortable: true,
      omit: !displayProperties.requestDate,
      cell: (row) => new Date(row.requestDate).toLocaleDateString(),
    },
    {
      name: 'Requested By',
      selector: (row) => row.requestedBy,
      sortable: true,
      omit: !displayProperties.requestedBy,
      cell: (row) => (
        <div className="text-sm">
          <div className="font-medium">{row.requestedBy}</div>
          <div className="text-gray-500">{row.department}</div>
        </div>
      ),
    },
    {
      name: 'Product(s)',
      selector: (row) => row.products,
      omit: !displayProperties.products,
      cell: (row) => (
        <div className="text-sm">
          {row.products.length === 1 ? (
            <div>{row.products[0].name}</div>
          ) : (
            <div>{row.products.length} products</div>
          )}
        </div>
      ),
    },
    {
      name: 'Quantity',
      selector: (row) => row.quantity,
      sortable: true,
      omit: !displayProperties.quantity,
      cell: (row) => `${row.quantity.toLocaleString()} items`,
    },
    {
      name: 'Priority',
      selector: (row) => row.priority,
      sortable: true,
      omit: !displayProperties.priority,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(row.priority)}`}>
          {row.priority}
        </span>
      ),
    },
    {
      name: 'Required By Date',
      selector: (row) => row.requiredByDate,
      sortable: true,
      omit: !displayProperties.requiredByDate,
      cell: (row) => new Date(row.requiredByDate).toLocaleDateString(),
    },
    {
      name: 'Request Status',
      selector: (row) => row.requestStatus,
      sortable: true,
      omit: !displayProperties.requestStatus,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(row.requestStatus)}`}>
          {row.requestStatus}
        </span>
      ),
    },
    {
      name: 'Notes / Justification',
      selector: (row) => row.notesJustification,
      omit: !displayProperties.notesJustification,
      cell: (row) => (
        <div className="text-sm max-w-[200px] truncate" title={row.notesJustification}>
          {row.notesJustification}
        </div>
      ),
    },
    {
      name: 'Actions',
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewRequest(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Details"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-eye text-base" />
          </button>
          {row.requestStatus === 'Pending' && (
            <>
              <button 
                data-tooltip-id="approve-tooltip"
                data-tooltip-content="Approve"
                className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-green-500/10 hover:text-green-500 rounded-lg cursor-pointer transition-base">
                {/* Approve */}
                <span className="icon icon-check-3 text-base" />
              </button>
              <button 
                data-tooltip-id="cancel-tooltip"
                data-tooltip-content="Cancel"
                className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base">
                {/* Cancel */}
                <span className="icon icon-x text-base" />
              </button>
            </>
          )}
          {row.requestStatus === 'Reviewed' && (
            <button 
              data-tooltip-id="convert-tooltip"
              data-tooltip-content="Convert"
              className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-success-500/10 hover:text-success-500 rounded-lg cursor-pointer transition-base">
              {/* Convert */}
              <span className="icon icon-check-3 text-base" />
            </button>
          )}
          <button 
            data-tooltip-id="edit-tooltip"
            data-tooltip-content="Edit"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-primary-500/10 hover:text-primary-500 rounded-lg cursor-pointer transition-base">
            {/* Edit */}
            <span className="icon icon-pencil-line text-base" />
          </button>

          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="approve-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="cancel-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="convert-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="edit-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      width: '160px',
      // allowOverflow: true,
      // button: true,
    },
  ];

  const handleViewRequest = (request) => {
    setSelectedRequest(request);
    setIsRequestDetailOpen(true);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);
        const newItems = [...items];
        newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, active.id);
        return newItems;
      });
    }
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  const filteredRequests = requests.filter((request) =>
    request.requestedBy.toLowerCase().includes(filterText.toLowerCase()) ||
    request.requestId.toLowerCase().includes(filterText.toLowerCase()) ||
    request.department.toLowerCase().includes(filterText.toLowerCase())
  );

  return (
    <div className="space-y-4">
      <div className="rounded-xl">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
        >
          <div className="absolute top-full right-0 mt-1 w-[200px] bg-white rounded-xl shadow-custom p-2 z-20">
            <DndContext
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={items}
                strategy={verticalListSortingStrategy}
              >
                <ul className="space-y-1">
                  {items.map((key) => (
                    <SortableItem
                      key={key}
                      id={key}
                      value={key}
                      checked={displayProperties[key]}
                      onChange={() =>
                        setDisplayProperties((prev) => ({
                          ...prev,
                          [key]: !prev[key],
                        }))
                      }
                    />
                  ))}
                </ul>
              </SortableContext>
            </DndContext>
          </div>
        </FilterField>

        {isLoading ? (
          <TableSkeleton 
            rowsPerPage={8}
            columns={[
              { size: 'small', grow: 1 },
              { size: 'small' },
              { size: 'medium', grow: 1 },
              { size: 'medium' },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'large', grow: 1 },
              { size: 'large' },
            ]}
            showCheckbox={true}
            cellHeight={4}
          />
        ) : (
          <DataTable
            columns={columns}
            data={filteredRequests}
            customStyles={customStyles}
            pagination
            selectableRows
            fixedHeader={true}
            selectableRowsHighlight
            selectableRowsComponent={CustomCheckbox}
            onSelectedRowsChange={handleSelectedRowsChange}
            selectableRowsComponentProps={{ 'aria-label': 'Select Row' }}
            className="custom-table auto-height-table"
            sortIcon={<SortIcon sortDirection={sortDirection} />}
            onSort={handleSort}
            sortField={sortedField}
            defaultSortAsc={true}
            paginationPerPage={8}
            paginationRowsPerPageOptions={[8]}
            paginationComponentOptions={{
              rowsPerPageText: 'Rows per page:',
              rangeSeparatorText: 'of',
              selectAllRowsItem: false,
              noRowsPerPage: true,
            }}
            paginationComponent={CommonPagination}
          />
        )}
      </div>

      {/* Request Detail Modal */}
      <RequestDetailModal
        isOpen={isRequestDetailOpen}
        onClose={() => setIsRequestDetailOpen(false)}
        request={selectedRequest}
      />
    </div>
  );
};

export default PurchaseRequests;
