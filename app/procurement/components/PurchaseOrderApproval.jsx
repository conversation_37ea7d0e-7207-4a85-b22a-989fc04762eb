'use client';

import React, { useState, useRef } from 'react';
import DataTable from 'react-data-table-component';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import FilterField from '../../components/table/FilterField';
import SortableItem from '../../components/table/SortableItem';
import SortIcon from '../../components/table/SortIcon';
import TableSkeleton from '../../components/table/TableSkeleton';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';
import { Tooltip } from 'react-tooltip';
import CommonPagination from '../../components/table/CommonPagination';

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const PurchaseOrderDetailModal = ({ isOpen, onClose, purchaseOrder }) => {
  if (!purchaseOrder) return null;

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title={`Purchase Order - ${purchaseOrder.poNumber}`}
      size="lg"
    >
      <div className="p-4 space-y-6">
        {/* PO Information */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-3">Purchase Order Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm text-gray-600">PO Number</label>
              <p className="font-medium">{purchaseOrder.poNumber}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600">PO Date</label>
              <p className="font-medium">{new Date(purchaseOrder.poDate).toLocaleDateString()}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600">Vendor Name</label>
              <p className="font-medium">{purchaseOrder.vendorName}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600">Total Amount</label>
              <p className="font-medium text-lg text-green-600">${purchaseOrder.totalAmount.toLocaleString()}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600">Delivery Schedule</label>
              <p className="font-medium">{new Date(purchaseOrder.deliverySchedule).toLocaleDateString()}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600">Payment Terms</label>
              <p className="font-medium">{purchaseOrder.paymentTerms}</p>
            </div>
          </div>
        </div>

        {/* Vendor Details */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-3">Vendor Details</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm text-gray-600">Contact Person</label>
              <p className="font-medium">{purchaseOrder.vendorDetails.contactPerson}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600">Email</label>
              <p className="font-medium">{purchaseOrder.vendorDetails.email}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600">Phone</label>
              <p className="font-medium">{purchaseOrder.vendorDetails.phone}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600">Address</label>
              <p className="font-medium">{purchaseOrder.vendorDetails.address}</p>
            </div>
          </div>
        </div>

        {/* Products */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-3">Products</h4>
          <div className="space-y-3">
            {purchaseOrder.products.map((product, index) => (
              <div key={index} className="flex justify-between items-center p-3 bg-white rounded border">
                <div>
                  <p className="font-medium">{product.name}</p>
                  <p className="text-sm text-gray-600">SKU: {product.sku}</p>
                  <p className="text-sm text-gray-600">Specification: {product.specification}</p>
                </div>
                <div className="text-right">
                  <p className="font-medium">{product.quantity.toLocaleString()} {product.unit}</p>
                  <p className="text-sm text-gray-600">${product.unitPrice.toFixed(2)} per {product.unit}</p>
                  <p className="text-sm font-medium text-green-600">
                    ${(product.quantity * product.unitPrice).toFixed(2)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Billing & Shipping */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-3">Billing & Shipping Addresses</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm text-gray-600">Billing Address</label>
              <p className="text-sm">{purchaseOrder.billingAddress}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600">Shipping Address</label>
              <p className="text-sm">{purchaseOrder.shippingAddress}</p>
            </div>
          </div>
        </div>

        {/* Terms & Conditions */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-3">Payment & Delivery Terms</h4>
          <p className="text-sm">{purchaseOrder.termsConditions}</p>
        </div>

        {/* Approval Information */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-3">Approval Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm text-gray-600">PO Status</label>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                purchaseOrder.poStatus === 'Approved' ? 'bg-green-100 text-green-800' : 
                purchaseOrder.poStatus === 'Pending Approval' ? 'bg-yellow-100 text-yellow-800' :
                purchaseOrder.poStatus === 'Draft' ? 'bg-gray-100 text-gray-800' :
                'bg-red-100 text-red-800'
              }`}>
                {purchaseOrder.poStatus}
              </span>
            </div>
            <div>
              <label className="text-sm text-gray-600">Approved By</label>
              <p className="font-medium">{purchaseOrder.approvedBy || 'Pending'}</p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4 border-t">
          {purchaseOrder.poStatus === 'Pending Approval' && (
            <>
              <button className="btn btn-primary">Approve PO</button>
              <button className="btn btn-outline-red">Reject PO</button>
            </>
          )}
          {purchaseOrder.poStatus === 'Approved' && (
            <button className="btn btn-outline-gray">Download PO</button>
          )}
          {purchaseOrder.poStatus === 'Draft' && (
            <button className="btn btn-primary">Submit for Approval</button>
          )}
          <button className="btn btn-outline-gray">Edit PO</button>
        </div>
      </div>
    </BaseOffCanvas>
  );
};

const PurchaseOrderApproval = ({ isLoading = false }) => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedPO, setSelectedPO] = useState(null);
  const [isPODetailOpen, setIsPODetailOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const displayMenuRef = useRef(null);

  const [displayProperties, setDisplayProperties] = useState({
    poNumber: true,
    poDate: true,
    vendorName: true,
    products: true,
    quantity: true,
    unitPrice: true,
    totalAmount: true,
    deliverySchedule: true,
    poStatus: true,
    approvedBy: true,
    paymentTerms: true,
  });

  const [items, setItems] = useState(Object.keys(displayProperties));

  // Sample data - in real app this would come from API
  const [purchaseOrders] = useState([
    {
      id: 'PO-001',
      poNumber: 'PO-001',
      poDate: '2024-01-22T09:00:00Z',
      vendorName: 'CompuWorld Inc',
      products: [
        { name: 'Laptops', sku: 'LP-002', quantity: 10, unit: 'units', specification: 'Intel i7, 16GB RAM, 512GB SSD', unitPrice: 1200.00 },
        { name: 'Monitors', sku: 'MN-002', quantity: 10, unit: 'units', specification: '24-inch, 1080p, IPS', unitPrice: 300.00 }
      ],
      quantity: 20,
      unitPrice: 1200.00,
      totalAmount: 15000,
      deliverySchedule: '2024-02-05T00:00:00Z',
      poStatus: 'Approved',
      approvedBy: 'Manager Johnson',
      paymentTerms: 'Net 15',
      vendorDetails: {
        contactPerson: 'Bob Smith',
        email: '<EMAIL>',
        phone: '******-0102',
        address: '123 Tech Street, Silicon Valley, CA 94000'
      },
      billingAddress: 'HubSups Inc, 456 Business Ave, Corporate City, NY 10001',
      shippingAddress: 'HubSups Warehouse, 789 Storage Blvd, Logistics Park, NJ 07001',
      termsConditions: 'Payment due within 15 days of delivery. 2-year warranty included. Express delivery available.'
    },
    {
      id: 'PO-002',
      poNumber: 'PO-002',
      poDate: '2024-01-21T14:30:00Z',
      vendorName: 'OfficePlus Furniture',
      products: [
        { name: 'Office Chairs', sku: 'OC-002', quantity: 25, unit: 'units', specification: 'Ergonomic, adjustable height', unitPrice: 240.00 }
      ],
      quantity: 25,
      unitPrice: 240.00,
      totalAmount: 6000,
      deliverySchedule: '2024-02-10T00:00:00Z',
      poStatus: 'Pending Approval',
      approvedBy: null,
      paymentTerms: 'Net 30',
      vendorDetails: {
        contactPerson: 'Carol Davis',
        email: '<EMAIL>',
        phone: '******-0103',
        address: '456 Furniture Row, Design District, TX 75001'
      },
      billingAddress: 'HubSups Inc, 456 Business Ave, Corporate City, NY 10001',
      shippingAddress: 'HubSups Office, 321 Corporate Plaza, Business District, NY 10002',
      termsConditions: 'Payment due within 30 days. 5-year warranty on all chairs. Assembly service included.'
    },
    {
      id: 'PO-003',
      poNumber: 'PO-003',
      poDate: '2024-01-20T11:00:00Z',
      vendorName: 'CleanCorp Supplies',
      products: [
        { name: 'Cleaning Supplies', sku: 'CS-003', quantity: 100, unit: 'units', specification: 'Eco-friendly cleaning products', unitPrice: 12.50 }
      ],
      quantity: 100,
      unitPrice: 12.50,
      totalAmount: 1250,
      deliverySchedule: '2024-02-01T00:00:00Z',
      poStatus: 'Draft',
      approvedBy: null,
      paymentTerms: 'COD',
      vendorDetails: {
        contactPerson: 'David Wilson',
        email: '<EMAIL>',
        phone: '******-0104',
        address: '789 Supply Chain Ave, Industrial Zone, IL 60601'
      },
      billingAddress: 'HubSups Inc, 456 Business Ave, Corporate City, NY 10001',
      shippingAddress: 'HubSups Facilities, 654 Maintenance St, Service Area, NJ 07002',
      termsConditions: 'Cash on delivery. Bulk discount applied for orders above 200 units.'
    },
    {
      id: 'PO-004',
      poNumber: 'PO-004',
      poDate: '2024-01-19T16:15:00Z',
      vendorName: 'PrintPro Materials',
      products: [
        { name: 'Promotional Materials', sku: 'PM-004', quantity: 500, unit: 'pieces', specification: 'Branded pens, notebooks, stickers', unitPrice: 5.00 }
      ],
      quantity: 500,
      unitPrice: 5.00,
      totalAmount: 2500,
      deliverySchedule: '2024-02-08T00:00:00Z',
      poStatus: 'Cancelled',
      approvedBy: null,
      paymentTerms: 'Net 30',
      vendorDetails: {
        contactPerson: 'Emma Brown',
        email: '<EMAIL>',
        phone: '******-0105',
        address: '321 Print Street, Creative Quarter, CA 90210'
      },
      billingAddress: 'HubSups Inc, 456 Business Ave, Corporate City, NY 10001',
      shippingAddress: 'HubSups Marketing, 987 Brand Blvd, Marketing Hub, NY 10003',
      termsConditions: 'Payment due within 30 days. Custom branding included. Rush orders available.'
    }
  ]);

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'pending approval':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns = [
    {
      name: 'PO Number',
      selector: (row) => row.poNumber,
      sortable: true,
      omit: !displayProperties.poNumber,
    },
    {
      name: 'PO Date',
      selector: (row) => row.poDate,
      sortable: true,
      omit: !displayProperties.poDate,
      cell: (row) => new Date(row.poDate).toLocaleDateString(),
    },
    {
      name: 'Vendor Name',
      selector: (row) => row.vendorName,
      sortable: true,
      omit: !displayProperties.vendorName,
    },
    {
      name: 'Product(s)',
      selector: (row) => row.products,
      omit: !displayProperties.products,
      cell: (row) => (
        <div className="text-sm">
          {row.products.length === 1 ? (
            <div>{row.products[0].name}</div>
          ) : (
            <div>{row.products.length} Product{row.products.length > 1 ? 's' : ''}</div>
          )}
        </div>
      ),
    },
    {
      name: 'Quantity',
      selector: (row) => row.quantity,
      sortable: true,
      omit: !displayProperties.quantity,
      cell: (row) => `${row.quantity.toLocaleString()} items`,
    },
    {
      name: 'Unit Price',
      selector: (row) => row.unitPrice,
      sortable: true,
      omit: !displayProperties.unitPrice,
      cell: (row) => `$${row.unitPrice.toFixed(2)}`,
    },
    {
      name: 'Total Amount',
      selector: (row) => row.totalAmount,
      sortable: true,
      omit: !displayProperties.totalAmount,
      cell: (row) => `$${row.totalAmount.toLocaleString()}`,
    },
    {
      name: 'Delivery Schedule',
      selector: (row) => row.deliverySchedule,
      sortable: true,
      omit: !displayProperties.deliverySchedule,
      cell: (row) => new Date(row.deliverySchedule).toLocaleDateString(),
    },
    {
      name: 'PO Status',
      selector: (row) => row.poStatus,
      sortable: true,
      width: '130px',
      omit: !displayProperties.poStatus,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-nowrap text-xs font-semibold rounded-full ${getStatusColor(row.poStatus)}`}>
          {row.poStatus}
        </span>
      ),
    },
    {
      name: 'Approved By',
      selector: (row) => row.approvedBy,
      sortable: true,
      omit: !displayProperties.approvedBy,
      cell: (row) => row.approvedBy || '-',
    },
    {
      name: 'Payment Terms',
      selector: (row) => row.paymentTerms,
      sortable: true,
      omit: !displayProperties.paymentTerms,
    },
    {
      name: 'Actions',
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewPO(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Details"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-eye text-base" />
          </button>
          {row.poStatus === 'Pending Approval' && (
            <>
              <button 
                data-tooltip-id="approve-tooltip"
                data-tooltip-content="Approve"
                className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-success-500/10 hover:text-success-500 rounded-lg cursor-pointer transition-base">
                {/* Approve */}
                <span className="icon icon-check-3 text-base" />
              </button>
              <button 
                data-tooltip-id="reject-tooltip"
                data-tooltip-content="Reject"
                className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base">
                {/* Reject */}
                <span className="icon icon-x text-base" />
              </button>
            </>
          )}
          {row.poStatus === 'Approved' && (
            <button 
              data-tooltip-id="download-tooltip"
              data-tooltip-content="Download"
              className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-info-500/10 hover:text-info-500 rounded-lg cursor-pointer transition-base">
              {/* Download */}
              <span className="icon icon-download text-base" />
            </button>
          )}
          {row.poStatus === 'Draft' && (
            <button 
              data-tooltip-id="edit-tooltip"
              data-tooltip-content="Edit"
              className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-primary-500/10 hover:text-primary-500 rounded-lg cursor-pointer transition-base">
              {/* Edit */}
              <span className="icon icon-pencil-line text-base" />
            </button>
          )}
          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="approve-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="reject-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="download-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="edit-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      width: '120px',
      // allowOverflow: true,
      // button: true,
    },
  ];

  const handleViewPO = (purchaseOrder) => {
    setSelectedPO(purchaseOrder);
    setIsPODetailOpen(true);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);
        const newItems = [...items];
        newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, active.id);
        return newItems;
      });
    }
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  const filteredPOs = purchaseOrders.filter((po) =>
    po.vendorName.toLowerCase().includes(filterText.toLowerCase()) ||
    po.poNumber.toLowerCase().includes(filterText.toLowerCase()) ||
    po.poStatus.toLowerCase().includes(filterText.toLowerCase())
  );

  return (
    <div className="space-y-4">
      <div className="rounded-xl">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
        >
          <div className="absolute top-full right-0 mt-1 w-[200px] bg-white rounded-xl shadow-custom p-2 z-20">
            <DndContext
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={items}
                strategy={verticalListSortingStrategy}
              >
                <ul className="space-y-1">
                  {items.map((key) => (
                    <SortableItem
                      key={key}
                      id={key}
                      value={key}
                      checked={displayProperties[key]}
                      onChange={() =>
                        setDisplayProperties((prev) => ({
                          ...prev,
                          [key]: !prev[key],
                        }))
                      }
                    />
                  ))}
                </ul>
              </SortableContext>
            </DndContext>
          </div>
        </FilterField>

        {isLoading ? (
          <TableSkeleton 
            rowsPerPage={8}
            columns={[
              { size: 'small', grow: 1 },
              { size: 'small' },
              { size: 'medium', grow: 1 },
              { size: 'medium' },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'large' },
            ]}
            showCheckbox={true}
            cellHeight={4}
          />
        ) : (
          <DataTable
            columns={columns}
            data={filteredPOs}
            customStyles={customStyles}
            pagination
            selectableRows
            fixedHeader={true}
            selectableRowsHighlight
            selectableRowsComponent={CustomCheckbox}
            onSelectedRowsChange={handleSelectedRowsChange}
            selectableRowsComponentProps={{ 'aria-label': 'Select Row' }}
            className="custom-table auto-height-table"
            sortIcon={<SortIcon sortDirection={sortDirection} />}
            onSort={handleSort}
            sortField={sortedField}
            defaultSortAsc={true}
            paginationPerPage={8}
            paginationRowsPerPageOptions={[8]}
            paginationComponentOptions={{
              rowsPerPageText: 'Rows per page:',
              rangeSeparatorText: 'of',
              selectAllRowsItem: false,
              noRowsPerPage: true,
            }}
            paginationComponent={(props) => (
              <CommonPagination
                selectedCount={props.selectedRows?.length}
                total={props.totalRows}
                page={props.currentPage}
                perPage={props.rowsPerPage}
                onPageChange={props.onChangePage}
              />
            )}
          />
        )}
      </div>

      {/* Purchase Order Detail Modal */}
      <PurchaseOrderDetailModal
        isOpen={isPODetailOpen}
        onClose={() => setIsPODetailOpen(false)}
        purchaseOrder={selectedPO}
      />
    </div>
  );
};

export default PurchaseOrderApproval;
