'use client';

import React, { useState, useRef } from 'react';
import DataTable from 'react-data-table-component';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import FilterField from '../../components/table/FilterField';
import SortableItem from '../../components/table/SortableItem';
import SortIcon from '../../components/table/SortIcon';
import TableSkeleton from '../../components/table/TableSkeleton';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';
import { Tooltip } from 'react-tooltip';
import CommonPagination from '../../components/table/CommonPagination';

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const ReturnDetailModal = ({ isOpen, onClose, returnItem }) => {
  if (!returnItem) return null;

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title={`Procurement Return - ${returnItem.returnId}`}
      size="lg"
    >
      <div className="p-4 space-y-6">
        {/* Return Information */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-3">Return Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm text-gray-600">Return ID</label>
              <p className="font-medium">{returnItem.returnId}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600">Linked PO Number</label>
              <p className="font-medium">{returnItem.linkedPONumber}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600">Vendor Name</label>
              <p className="font-medium">{returnItem.vendorName}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600">Return Date</label>
              <p className="font-medium">{new Date(returnItem.returnDate).toLocaleDateString()}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600">Reason for Return</label>
              <p className="font-medium">{returnItem.reasonForReturn}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600">Return Status</label>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                returnItem.returnStatus === 'Closed' ? 'bg-green-100 text-green-800' : 
                returnItem.returnStatus === 'Received by Vendor' ? 'bg-info-500/20 text-info-500' :
                returnItem.returnStatus === 'In Transit' ? 'bg-purple-100 text-purple-800' :
                'bg-yellow-100 text-yellow-800'
              }`}>
                {returnItem.returnStatus}
              </span>
            </div>
          </div>
        </div>

        {/* Products Returned */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-3">Products Returned</h4>
          <div className="space-y-3">
            {returnItem.productsReturned.map((product, index) => (
              <div key={index} className="flex justify-between items-center p-3 bg-white rounded border">
                <div>
                  <p className="font-medium">{product.name}</p>
                  <p className="text-sm text-gray-600">SKU: {product.sku}</p>
                  <p className="text-sm text-gray-600">Condition: {product.condition}</p>
                </div>
                <div className="text-right">
                  <p className="font-medium">{product.quantity.toLocaleString()} {product.unit}</p>
                  <p className="text-sm text-gray-600">Value: ${product.unitValue.toFixed(2)} each</p>
                  <p className="text-sm font-medium text-red-600">
                    Total: ${(product.quantity * product.unitValue).toFixed(2)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Vendor Contact */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-3">Vendor Contact Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm text-gray-600">Contact Person</label>
              <p className="font-medium">{returnItem.vendorContact.name}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600">Email</label>
              <p className="font-medium">{returnItem.vendorContact.email}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600">Phone</label>
              <p className="font-medium">{returnItem.vendorContact.phone}</p>
            </div>
            <div>
              <label className="text-sm text-gray-600">Return Address</label>
              <p className="font-medium">{returnItem.vendorContact.returnAddress}</p>
            </div>
          </div>
        </div>

        {/* Credit Note Information */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-3">Credit Note Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm text-gray-600">Credit Note Received</label>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                returnItem.creditNoteReceived === 'Yes' ? 'bg-green-100 text-green-800' : 
                returnItem.creditNoteReceived === 'No' ? 'bg-red-100 text-red-800' :
                'bg-yellow-100 text-yellow-800'
              }`}>
                {returnItem.creditNoteReceived}
              </span>
            </div>
            <div>
              <label className="text-sm text-gray-600">Credit Amount</label>
              <p className="font-medium">{returnItem.creditAmount ? `$${returnItem.creditAmount.toLocaleString()}` : 'Pending'}</p>
            </div>
          </div>
        </div>

        {/* Inspection Notes */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-3">Inspection Notes</h4>
          <p className="text-sm">{returnItem.inspectionNotes || 'No inspection notes available.'}</p>
        </div>

        {/* Courier Information */}
        {returnItem.courierInfo && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-semibold mb-3">Courier Information</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm text-gray-600">Courier Service</label>
                <p className="font-medium">{returnItem.courierInfo.service}</p>
              </div>
              <div>
                <label className="text-sm text-gray-600">Tracking Number</label>
                <p className="font-medium">{returnItem.courierInfo.trackingNumber}</p>
              </div>
              <div>
                <label className="text-sm text-gray-600">Pickup Date</label>
                <p className="font-medium">{new Date(returnItem.courierInfo.pickupDate).toLocaleDateString()}</p>
              </div>
              <div>
                <label className="text-sm text-gray-600">Expected Delivery</label>
                <p className="font-medium">{new Date(returnItem.courierInfo.expectedDelivery).toLocaleDateString()}</p>
              </div>
            </div>
          </div>
        )}

        {/* Product Images */}
        {returnItem.productImages && returnItem.productImages.length > 0 && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-semibold mb-3">Product Images</h4>
            <div className="grid grid-cols-3 gap-3">
              {returnItem.productImages.map((image, index) => (
                <div key={index} className="aspect-square bg-gray-200 rounded-lg flex items-center justify-center">
                  <span className="text-gray-500 text-sm">{image.name}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4 border-t">
          {returnItem.returnStatus === 'Initiated' && (
            <>
              <button className="btn btn-primary">Approve Return</button>
              <button className="btn btn-outline-gray">Schedule Pickup</button>
            </>
          )}
          {returnItem.returnStatus === 'In Transit' && (
            <button className="btn btn-outline-gray">Track Shipment</button>
          )}
          {returnItem.creditNoteReceived === 'No' && returnItem.returnStatus === 'Received by Vendor' && (
            <button className="btn btn-primary">Request Credit Note</button>
          )}
          <button className="btn btn-outline-gray">Upload Document</button>
        </div>
      </div>
    </BaseOffCanvas>
  );
};

const ProcurementReturns = ({ isLoading = false }) => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedReturn, setSelectedReturn] = useState(null);
  const [isReturnDetailOpen, setIsReturnDetailOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const displayMenuRef = useRef(null);

  const [displayProperties, setDisplayProperties] = useState({
    returnId: true,
    linkedPONumber: true,
    vendorName: true,
    returnDate: true,
    reasonForReturn: true,
    productsReturned: true,
    returnStatus: true,
    inspectionNotes: true,
    creditNoteReceived: true,
  });

  const [items, setItems] = useState(Object.keys(displayProperties));

  // Sample data - in real app this would come from API
  const [returns] = useState([
    {
      id: 'RET-P001',
      returnId: 'RET-P001',
      linkedPONumber: 'PO-001',
      vendorName: 'CompuWorld Inc',
      returnDate: '2024-01-25T10:00:00Z',
      reasonForReturn: 'Damage',
      productsReturned: [
        { name: 'Laptops', sku: 'LP-002', quantity: 2, unit: 'units', condition: 'Damaged during shipping', unitValue: 1200.00 }
      ],
      returnStatus: 'Closed',
      inspectionNotes: 'Units arrived with cracked screens. Packaging was insufficient for protection during transit.',
      creditNoteReceived: 'Yes',
      creditAmount: 2400,
      vendorContact: {
        name: 'Bob Smith',
        email: '<EMAIL>',
        phone: '******-0102',
        returnAddress: '123 Tech Street, Returns Dept, Silicon Valley, CA 94000'
      },
      courierInfo: {
        service: 'FedEx',
        trackingNumber: 'FX123456789',
        pickupDate: '2024-01-25T14:00:00Z',
        expectedDelivery: '2024-01-26T17:00:00Z'
      },
      productImages: [
        { name: 'damage_photo_1.jpg' },
        { name: 'damage_photo_2.jpg' }
      ]
    },
    {
      id: 'RET-P002',
      returnId: 'RET-P002',
      linkedPONumber: 'PO-002',
      vendorName: 'OfficePlus Furniture',
      returnDate: '2024-01-24T15:30:00Z',
      reasonForReturn: 'Incorrect Items',
      productsReturned: [
        { name: 'Office Chairs', sku: 'OC-003', quantity: 5, unit: 'units', condition: 'Wrong model delivered', unitValue: 240.00 }
      ],
      returnStatus: 'Received by Vendor',
      inspectionNotes: 'Received executive chairs instead of standard office chairs as ordered.',
      creditNoteReceived: 'Pending',
      creditAmount: null,
      vendorContact: {
        name: 'Carol Davis',
        email: '<EMAIL>',
        phone: '******-0103',
        returnAddress: '456 Furniture Row, Returns Center, Design District, TX 75001'
      },
      courierInfo: {
        service: 'UPS',
        trackingNumber: 'UP987654321',
        pickupDate: '2024-01-24T16:00:00Z',
        expectedDelivery: '2024-01-25T12:00:00Z'
      },
      productImages: []
    },
    {
      id: 'RET-P003',
      returnId: 'RET-P003',
      linkedPONumber: 'PO-003',
      vendorName: 'CleanCorp Supplies',
      returnDate: '2024-01-23T09:15:00Z',
      reasonForReturn: 'Short Supply',
      productsReturned: [
        { name: 'Cleaning Supplies', sku: 'CS-003', quantity: 20, unit: 'units', condition: 'Missing from shipment', unitValue: 12.50 }
      ],
      returnStatus: 'In Transit',
      inspectionNotes: 'Ordered 100 units but only received 80. Returning the shortage claim.',
      creditNoteReceived: 'No',
      creditAmount: null,
      vendorContact: {
        name: 'David Wilson',
        email: '<EMAIL>',
        phone: '******-0104',
        returnAddress: '789 Supply Chain Ave, Warehouse B, Industrial Zone, IL 60601'
      },
      courierInfo: {
        service: 'DHL',
        trackingNumber: 'DH456789123',
        pickupDate: '2024-01-23T11:00:00Z',
        expectedDelivery: '2024-01-24T16:00:00Z'
      },
      productImages: []
    },
    {
      id: 'RET-P004',
      returnId: 'RET-P004',
      linkedPONumber: 'PO-004',
      vendorName: 'PrintPro Materials',
      returnDate: '2024-01-22T13:45:00Z',
      reasonForReturn: 'Quality Issues',
      productsReturned: [
        { name: 'Promotional Materials', sku: 'PM-004', quantity: 50, unit: 'pieces', condition: 'Poor print quality', unitValue: 5.00 }
      ],
      returnStatus: 'Initiated',
      inspectionNotes: 'Print quality does not meet specifications. Colors are faded and text is blurry.',
      creditNoteReceived: 'No',
      creditAmount: null,
      vendorContact: {
        name: 'Emma Brown',
        email: '<EMAIL>',
        phone: '******-0105',
        returnAddress: '321 Print Street, QC Department, Creative Quarter, CA 90210'
      },
      courierInfo: null,
      productImages: [
        { name: 'quality_issue_1.jpg' },
        { name: 'quality_issue_2.jpg' }
      ]
    }
  ]);

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'initiated':
        return 'bg-info-500/20 text-info-500';
      case 'in transit':
        return 'bg-purple-100 text-purple-800';
      case 'received by vendor':
        return 'bg-yellow-100 text-yellow-800';
      case 'closed':
        return 'bg-green-100 text-green-800';
      case 'yes':
        return 'bg-green-100 text-green-800';
      case 'no':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns = [
    {
      name: 'Return ID',
      selector: (row) => row.returnId,
      sortable: true,
      omit: !displayProperties.returnId,
    },
    {
      name: 'Linked PO Number',
      selector: (row) => row.linkedPONumber,
      sortable: true,
      omit: !displayProperties.linkedPONumber,
    },
    {
      name: 'Vendor Name',
      selector: (row) => row.vendorName,
      sortable: true,
      omit: !displayProperties.vendorName,
    },
    {
      name: 'Return Date',
      selector: (row) => row.returnDate,
      sortable: true,
      omit: !displayProperties.returnDate,
      cell: (row) => new Date(row.returnDate).toLocaleDateString(),
    },
    {
      name: 'Reason for Return',
      selector: (row) => row.reasonForReturn,
      sortable: true,
      omit: !displayProperties.reasonForReturn,
    },
    {
      name: 'Product(s) Returned',
      selector: (row) => row.productsReturned,
      omit: !displayProperties.productsReturned,
      cell: (row) => (
        <div className="text-sm">
          {row.productsReturned.length === 1 ? (
            <div>
              <div>{row.productsReturned[0].name}</div>
              <div className="text-gray-400 text-xs">{row.productsReturned[0].quantity} {row.productsReturned[0].unit}</div>
            </div>
          ) : (
            <div>{row.productsReturned.length} products</div>
          )}
        </div>
      ),
    },
    {
      name: 'Return Status',
      selector: (row) => row.returnStatus,
      sortable: true,
      omit: !displayProperties.returnStatus,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(row.returnStatus)}`}>
          {row.returnStatus}
        </span>
      ),
    },
    {
      name: 'Inspection Notes',
      selector: (row) => row.inspectionNotes,
      omit: !displayProperties.inspectionNotes,
      cell: (row) => (
        <div className="text-sm max-w-[200px] truncate" title={row.inspectionNotes}>
          {row.inspectionNotes}
        </div>
      ),
    },
    {
      name: 'Credit Note Received',
      selector: (row) => row.creditNoteReceived,
      sortable: true,
      omit: !displayProperties.creditNoteReceived,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(row.creditNoteReceived)}`}>
          {row.creditNoteReceived}
        </span>
      ),
    },
    {
      name: 'Actions',
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewReturn(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Details"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-eye text-base" />
          </button>
          {row.returnStatus === 'Initiated' && (
            <>
              <button 
                data-tooltip-id="approve-tooltip"
                data-tooltip-content="Approve"
                className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-green-500/10 hover:text-green-500 rounded-lg cursor-pointer transition-base">
                <span className="icon icon-check-3 text-base" />
              </button>
              <button 
                data-tooltip-id="reject-tooltip"
                data-tooltip-content="Reject"
                className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base">
                <span className="icon icon-x text-base" />
              </button>
            </>
          )}
          {row.creditNoteReceived === 'No' && row.returnStatus === 'Received by Vendor' && (
            <button 
              data-tooltip-id="request-credit-tooltip"
              data-tooltip-content="Request Credit"
              className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-primary-500/10 hover:text-primary-500 rounded-lg cursor-pointer transition-base">
              {/* Request Credit */}
              <span className="icon icon-file-text text-base" />
            </button>
          )}
          <button 
            data-tooltip-id="upload-doc-tooltip"
            data-tooltip-content="Upload Document"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base">
            <span className="icon icon-upload text-base" />
          </button>

          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="approve-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="reject-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="request-credit-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="upload-doc-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      width: '155px',
      // allowOverflow: true,
      // button: true,
    },
  ];

  const handleViewReturn = (returnItem) => {
    setSelectedReturn(returnItem);
    setIsReturnDetailOpen(true);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);
        const newItems = [...items];
        newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, active.id);
        return newItems;
      });
    }
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  const filteredReturns = returns.filter((returnItem) =>
    returnItem.returnId.toLowerCase().includes(filterText.toLowerCase()) ||
    returnItem.linkedPONumber.toLowerCase().includes(filterText.toLowerCase()) ||
    returnItem.vendorName.toLowerCase().includes(filterText.toLowerCase()) ||
    returnItem.reasonForReturn.toLowerCase().includes(filterText.toLowerCase())
  );

  return (
    <div className="space-y-4">
      <div className="rounded-xl">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
        >
          <div className="absolute top-full right-0 mt-1 w-[200px] bg-white rounded-xl shadow-custom p-2 z-20">
            <DndContext
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={items}
                strategy={verticalListSortingStrategy}
              >
                <ul className="space-y-1">
                  {items.map((key) => (
                    <SortableItem
                      key={key}
                      id={key}
                      value={key}
                      checked={displayProperties[key]}
                      onChange={() =>
                        setDisplayProperties((prev) => ({
                          ...prev,
                          [key]: !prev[key],
                        }))
                      }
                    />
                  ))}
                </ul>
              </SortableContext>
            </DndContext>
          </div>
        </FilterField>

        {isLoading ? (
          <TableSkeleton 
            rowsPerPage={8}
            columns={[
              { size: 'small', grow: 1 },
              { size: 'small' },
              { size: 'medium', grow: 1 },
              { size: 'small' },
              { size: 'medium' },
              { size: 'medium', grow: 1 },
              { size: 'small' },
              { size: 'large', grow: 1 },
              { size: 'small' },
              { size: 'large' },
            ]}
            showCheckbox={true}
            cellHeight={4}
          />
        ) : (
          <DataTable
            columns={columns}
            data={filteredReturns}
            customStyles={customStyles}
            pagination
            selectableRows
            fixedHeader={true}
            selectableRowsHighlight
            selectableRowsComponent={CustomCheckbox}
            onSelectedRowsChange={handleSelectedRowsChange}
            selectableRowsComponentProps={{ 'aria-label': 'Select Row' }}
            className="custom-table auto-height-table"
            sortIcon={<SortIcon sortDirection={sortDirection} />}
            onSort={handleSort}
            sortField={sortedField}
            defaultSortAsc={true}
            paginationPerPage={8}
            paginationRowsPerPageOptions={[8]}
            paginationComponentOptions={{
              rowsPerPageText: 'Rows per page:',
              rangeSeparatorText: 'of',
              selectAllRowsItem: false,
              noRowsPerPage: true,
            }}
            paginationComponent={(props) => (
              <CommonPagination
                selectedCount={props.selectedRows?.length}
                total={props.totalRows}
                page={props.currentPage}
                perPage={props.rowsPerPage}
                onPageChange={props.onChangePage}
              />
            )}
          />
        )}
      </div>

      {/* Return Detail Modal */}
      <ReturnDetailModal
        isOpen={isReturnDetailOpen}
        onClose={() => setIsReturnDetailOpen(false)}
        returnItem={selectedReturn}
      />
    </div>
  );
};

export default ProcurementReturns;
