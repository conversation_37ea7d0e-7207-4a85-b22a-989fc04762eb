'use client';

import React, { useState, useRef } from 'react';
import DataTable from 'react-data-table-component';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import FilterField from '../../components/table/FilterField';
import SortableItem from '../../components/table/SortableItem';
import SortIcon from '../../components/table/SortIcon';
import TableSkeleton from '../../components/table/TableSkeleton';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';
import { Tooltip } from 'react-tooltip';
import CommonPagination from '../../components/table/CommonPagination';


// Info row component
const InfoRow = ({ label, value, className }) => (
  <div className={`flex flex-col gap-1 ${className}`}>
    <span className="text-sm text-gray-500/60 font-normal">{label}</span>
    <span className="text-sm text-dark-500 font-medium">{value}</span>
  </div>
);

// Custom checkbox component
const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const QuotationDetailModal = ({ isOpen, onClose, quotation }) => {
  if (!quotation) return null;

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title={`Quotation Details - ${quotation.quotationId}`}
      size="md"
    >
      <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
        {/* Quotation Information */}
        <div className="">
          <h4 className="font-semibold mb-3">Quotation Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Quotation ID" value={quotation.quotationId} />
            <InfoRow label="Linked Request ID" value={quotation.linkedRequestId} />
            <InfoRow label="Vendor Name" value={quotation.vendorName} />
            <InfoRow label="Submitted Date" value={new Date(quotation.submittedDate).toLocaleDateString()} />
            <InfoRow label="Lead Time" value={quotation.leadTimeDays + ' days'} />
            <InfoRow label="Quoted Price per Unit" value={`$${quotation.quotedPricePerUnit.toFixed(2)}`} />
            <InfoRow label="Quotation Validity" value={new Date(quotation.quotationValidity).toLocaleDateString()} />
            <InfoRow label="Status" value={quotation.status} />
          </div>
        </div>
        <hr/>

        {/* Products Quoted */}
        <div className="">
          <h4 className="font-semibold mb-3">Products Quoted</h4>
          <div className="space-y-3">
            {quotation.productsQuoted.map((product, index) => (
              <div key={index} className="flex justify-between items-start p-3 border border-border-color rounded-lg">
                <div className="flex flex-col gap-1">
                  <p className="font-medium text-sm">{product.name}</p>
                  <p className="text-xs text-gray-400">SKU: {product.sku}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-400">Qty: {product.quantity.toLocaleString()}</p>
                  <p className="text-sm font-medium text-gray-500">${product.quotedPricePerUnit.toFixed(2)} per {product.unit}</p>
                  <p className="text-sm font-medium text-dark-500">
                    Total: ${(product.quotedPricePerUnit * product.quantity).toFixed(2)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
        <hr/>

        {/* Vendor Details */}
        <div className="">
          <h4 className="font-semibold mb-3">Vendor Details</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Vendor Name" value={quotation.vendorName} />
            <InfoRow label="Email" value={quotation.vendorContact.email} />
            <InfoRow label="Phone" value={quotation.vendorContact.phone} />
            <InfoRow label="Payment Terms" value={quotation.paymentTerms} />
          </div>
        </div>
        <hr/>

        {/* Terms & Conditions */}
        <div className="">
          <h4 className="font-semibold mb-3">Terms & Conditions</h4>
          <p className="text-sm">{quotation.termsConditions}</p>
        </div>

        {/* Attachments */}
        {quotation.attachments && quotation.attachments.length > 0 && (
          <>
            <hr/>
            <div className="">
              <h4 className="font-semibold mb-3">Attachments</h4>
              <div className="space-y-2">
                {quotation.attachments.map((file, index) => (
                  <div key={index} className="flex items-center gap-1 justify-between p-3 border border-border-color rounded-lg">
                    <span className="icon icon-file-pdf"></span>
                    <span className="text-sm">{file.name}</span>
                    <span className="text-xs text-gray-500">({file.size})</span>
                    <button className="text-primary-500 hover:text-primary-600 text-sm ml-auto">
                      Download
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

      </div>
      {/* Action Buttons */}
      <div className="flex justify-end gap-3 border-t border-border-color p-4">
        {quotation.status === 'Pending' && (
          <>
            <button className="btn btn-primary">Accept Quotation</button>
            <button className="btn btn-outline-danger">Reject Quotation</button>
          </>
        )}
        <button className="btn btn-outline-gray">Compare Quotations</button>
        <button className="btn btn-gray">Contact Vendor</button>
      </div>
    </BaseOffCanvas>
  );
};

const VendorQuotations = ({ isLoading = false }) => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedQuotation, setSelectedQuotation] = useState(null);
  const [isQuotationDetailOpen, setIsQuotationDetailOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const displayMenuRef = useRef(null);

  const [displayProperties, setDisplayProperties] = useState({
    quotationId: true,
    linkedRequestId: true,
    vendorName: true,
    submittedDate: true,
    productsQuoted: true,
    quotedPricePerUnit: true,
    leadTimeDays: true,
    quotationValidity: true,
    status: true,
    attachments: true,
  });

  const [items, setItems] = useState(Object.keys(displayProperties));

  // Sample data - in real app this would come from API
  const [quotations] = useState([
    {
      id: 'QUO-001',
      quotationId: 'QUO-001',
      linkedRequestId: 'PR-001',
      vendorName: 'TechCorp Solutions',
      submittedDate: '2024-01-21T10:00:00Z',
      productsQuoted: [
        { name: 'Laptops', sku: 'LP-001', quantity: 10, unit: 'units', specification: 'Intel i7, 16GB RAM, 512GB SSD', quotedPricePerUnit: 1150.00 },
        { name: 'Monitors', sku: 'MN-001', quantity: 10, unit: 'units', specification: '24-inch, 1080p, IPS', quotedPricePerUnit: 280.00 }
      ],
      quotedPricePerUnit: 1150.00,
      leadTimeDays: 7,
      quotationValidity: '2024-02-21T00:00:00Z',
      status: 'Pending',
      vendorContact: {
        name: 'Alice Johnson',
        email: '<EMAIL>',
        phone: '******-0101'
      },
      paymentTerms: 'Net 30',
      termsConditions: 'Standard warranty applies. Free delivery for orders above $10,000.',
      attachments: [
        { name: 'quotation_techcorp.pdf', size: '245KB' },
        { name: 'product_specs.pdf', size: '156KB' }
      ]
    },
    {
      id: 'QUO-002',
      quotationId: 'QUO-002',
      linkedRequestId: 'PR-001',
      vendorName: 'CompuWorld Inc',
      submittedDate: '2024-01-21T14:30:00Z',
      productsQuoted: [
        { name: 'Laptops', sku: 'LP-002', quantity: 10, unit: 'units', specification: 'Intel i7, 16GB RAM, 512GB SSD', quotedPricePerUnit: 1200.00 },
        { name: 'Monitors', sku: 'MN-002', quantity: 10, unit: 'units', specification: '24-inch, 1080p, IPS', quotedPricePerUnit: 300.00 }
      ],
      quotedPricePerUnit: 1200.00,
      leadTimeDays: 5,
      quotationValidity: '2024-02-20T00:00:00Z',
      status: 'Accepted',
      vendorContact: {
        name: 'Bob Smith',
        email: '<EMAIL>',
        phone: '******-0102'
      },
      paymentTerms: 'Net 15',
      termsConditions: '2-year warranty included. Express delivery available.',
      attachments: [
        { name: 'quotation_compuworld.pdf', size: '198KB' }
      ]
    },
    {
      id: 'QUO-003',
      quotationId: 'QUO-003',
      linkedRequestId: 'PR-002',
      vendorName: 'OfficePlus Furniture',
      submittedDate: '2024-01-20T09:15:00Z',
      productsQuoted: [
        { name: 'Office Chairs', sku: 'OC-002', quantity: 25, unit: 'units', specification: 'Ergonomic, adjustable height', quotedPricePerUnit: 240.00 }
      ],
      quotedPricePerUnit: 240.00,
      leadTimeDays: 14,
      quotationValidity: '2024-02-19T00:00:00Z',
      status: 'Pending',
      vendorContact: {
        name: 'Carol Davis',
        email: '<EMAIL>',
        phone: '******-0103'
      },
      paymentTerms: 'Net 30',
      termsConditions: '5-year warranty on all chairs. Assembly service included.',
      attachments: [
        { name: 'chair_catalog.pdf', size: '2.1MB' }
      ]
    },
    {
      id: 'QUO-004',
      quotationId: 'QUO-004',
      linkedRequestId: 'PR-003',
      vendorName: 'CleanCorp Supplies',
      submittedDate: '2024-01-19T16:00:00Z',
      productsQuoted: [
        { name: 'Cleaning Supplies', sku: 'CS-003', quantity: 100, unit: 'units', specification: 'Eco-friendly cleaning products', quotedPricePerUnit: 12.50 }
      ],
      quotedPricePerUnit: 12.50,
      leadTimeDays: 3,
      quotationValidity: '2024-02-18T00:00:00Z',
      status: 'Rejected',
      vendorContact: {
        name: 'David Wilson',
        email: '<EMAIL>',
        phone: '******-0104'
      },
      paymentTerms: 'COD',
      termsConditions: 'Bulk discount available for orders above 200 units.',
      attachments: []
    }
  ]);

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns = [
    {
      name: 'Quotation ID',
      selector: (row) => row.quotationId,
      sortable: true,
      omit: !displayProperties.quotationId,
    },
    {
      name: 'Linked Request ID',
      selector: (row) => row.linkedRequestId,
      sortable: true,
      omit: !displayProperties.linkedRequestId,
    },
    {
      name: 'Vendor Name',
      selector: (row) => row.vendorName,
      sortable: true,
      omit: !displayProperties.vendorName,
    },
    {
      name: 'Submitted Date',
      selector: (row) => row.submittedDate,
      sortable: true,
      omit: !displayProperties.submittedDate,
      cell: (row) => new Date(row.submittedDate).toLocaleDateString(),
    },
    {
      name: 'Product(s) Quoted',
      selector: (row) => row.productsQuoted,
      omit: !displayProperties.productsQuoted,
      cell: (row) => (
        <div className="text-sm">
          {row.productsQuoted.length === 1 ? (
            <div>{row.productsQuoted[0].name}</div>
          ) : (
            <div>{row.productsQuoted.length} products</div>
          )}
        </div>
      ),
    },
    {
      name: 'Quoted Price per Unit',
      selector: (row) => row.quotedPricePerUnit,
      sortable: true,
      omit: !displayProperties.quotedPricePerUnit,
      cell: (row) => `$${row.quotedPricePerUnit.toFixed(2)}`,
    },
    {
      name: 'Lead Time (Days)',
      selector: (row) => row.leadTimeDays,
      sortable: true,
      omit: !displayProperties.leadTimeDays,
      cell: (row) => `${row.leadTimeDays} days`,
    },
    {
      name: 'Quotation Validity',
      selector: (row) => row.quotationValidity,
      sortable: true,
      omit: !displayProperties.quotationValidity,
      cell: (row) => new Date(row.quotationValidity).toLocaleDateString(),
    },
    {
      name: 'Status',
      selector: (row) => row.status,
      sortable: true,
      omit: !displayProperties.status,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(row.status)}`}>
          {row.status}
        </span>
      ),
    },
    {
      name: 'Attachments',
      selector: (row) => row.attachments,
      omit: !displayProperties.attachments,
      cell: (row) => (
        <div className="text-sm">
          {row.attachments.length > 0 ? (
            <span className="text-info-600">{row.attachments.length} File{row.attachments.length > 1 ? 's' : ''}</span>
          ) : (
            <span className="text-gray-400">None</span>
          )}
        </div>
      ),
    },
    {
      name: 'Actions',
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewQuotation(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Details"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-eye text-base" />
          </button>
          {row.status === 'Pending' && (
            <>
              <button 
                data-tooltip-id="accept-tooltip"
                data-tooltip-content="Accept"
                className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-success-500/10 hover:text-success-500 rounded-lg cursor-pointer transition-base">
                {/* Accept */}
                <span className="icon icon-check-3 text-base" />
              </button>
              <button 
                data-tooltip-id="reject-tooltip"
                data-tooltip-content="Reject"
                className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base">
                {/* Reject */}
                <span className="icon icon-x text-base" />
              </button>
            </>
          )}
          <button 
            data-tooltip-id="compare-tooltip"
            data-tooltip-content="Compare"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-info-500/10 hover:text-info-500 rounded-lg cursor-pointer transition-base">
            {/* Compare */}
            <span className="icon icon-square-split-horizontal text-base" />
          </button>

          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="accept-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="reject-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="compare-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      width: '150px',
      // allowOverflow: true,
      // button: true,
    },
  ];

  const handleViewQuotation = (quotation) => {
    setSelectedQuotation(quotation);
    setIsQuotationDetailOpen(true);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);
        const newItems = [...items];
        newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, active.id);
        return newItems;
      });
    }
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  const filteredQuotations = quotations.filter((quotation) =>
    quotation.vendorName.toLowerCase().includes(filterText.toLowerCase()) ||
    quotation.quotationId.toLowerCase().includes(filterText.toLowerCase()) ||
    quotation.linkedRequestId.toLowerCase().includes(filterText.toLowerCase())
  );

  return (
    <div className="space-y-4">
      <div className="rounded-xl">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
        >
          <div className="absolute top-full right-0 mt-1 w-[200px] bg-white rounded-xl shadow-custom p-2 z-20">
            <DndContext
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={items}
                strategy={verticalListSortingStrategy}
              >
                <ul className="space-y-1">
                  {items.map((key) => (
                    <SortableItem
                      key={key}
                      id={key}
                      value={key}
                      checked={displayProperties[key]}
                      onChange={() =>
                        setDisplayProperties((prev) => ({
                          ...prev,
                          [key]: !prev[key],
                        }))
                      }
                    />
                  ))}
                </ul>
              </SortableContext>
            </DndContext>
          </div>
        </FilterField>

        {isLoading ? (
          <TableSkeleton 
            rowsPerPage={8}
            columns={[
              { size: 'small', grow: 1 },
              { size: 'small' },
              { size: 'medium', grow: 1 },
              { size: 'small' },
              { size: 'medium' },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'large' },
            ]}
            showCheckbox={true}
            cellHeight={4}
          />
        ) : (
          <DataTable
            columns={columns}
            data={filteredQuotations}
            customStyles={customStyles}
            pagination
            selectableRows
            fixedHeader={true}
            selectableRowsHighlight
            selectableRowsComponent={CustomCheckbox}
            onSelectedRowsChange={handleSelectedRowsChange}
            selectableRowsComponentProps={{ 'aria-label': 'Select Row' }}
            className="custom-table auto-height-table"
            sortIcon={<SortIcon sortDirection={sortDirection} />}
            onSort={handleSort}
            sortField={sortedField}
            defaultSortAsc={true}
            paginationPerPage={8}
            paginationRowsPerPageOptions={[8]}
            paginationComponentOptions={{
              rowsPerPageText: 'Rows per page:',
              rangeSeparatorText: 'of',
              selectAllRowsItem: false,
              noRowsPerPage: true,
            }}
            paginationComponent={(props) => (
              <CommonPagination
                selectedCount={props.selectedRows?.length}
                total={props.totalRows}
                page={props.currentPage}
                perPage={props.rowsPerPage}
                onPageChange={props.onChangePage}
              />
            )}
          />
        )}
      </div>

      {/* Quotation Detail Modal */}
      <QuotationDetailModal
        isOpen={isQuotationDetailOpen}
        onClose={() => setIsQuotationDetailOpen(false)}
        quotation={selectedQuotation}
      />
    </div>
  );
};

export default VendorQuotations;
