'use client';

import React, { useState } from 'react';
import PrivateLayout from '../components/layout/PrivateLayout';
import Sidebar from '../components/sidebar/Sidebar';

const Profile = () => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  return (
    <PrivateLayout>
      <div className="sm:flex bg-lighter-100 rounded-xl ml-3 w-[calc(100% - 12px)]">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />
        <div
          className={`content-wrapper collapsed-${isSidebarCollapsed} w-full sm:max-w-[calc(100%-${isSidebarCollapsed ? '80px' : '292px'})] p-6 ml-[${isSidebarCollapsed ? '80px' : '292px'}] mx-auto bg-white rounded-s-xl rounded-bl-xl overflow-auto h-[calc(100vh-60px)] transition-base`}
        >
          <div className="text-center mt-14 w-full max-w-6xl mx-auto h-[calc(100vh-173px)] flex items-center justify-center">
            <h1 className="text-3xl font-bold mb-8">Coming Soon...</h1>
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
};

export default Profile;
