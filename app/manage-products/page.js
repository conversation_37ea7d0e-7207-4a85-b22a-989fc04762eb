'use client';

import React, { useState } from 'react';
import PrivateLayout from '../components/layout/PrivateLayout';
import Sidebar from '../components/sidebar/Sidebar';
import Breadcrumb from '../components/Inputs/Breadcrumb';
import ProductsTab from './components/ProductsTab';
import ValueCombosTab from './components/ValueCombosTab';
import ProductSetsTab from './components/ProductSetsTab';


const ProductManagement = () => {
  const [activeTab, setActiveTab] = useState('products');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  const breadcrumbItems = [
    { label: 'Dashboard', link: '/dashboard' },
    { label: 'Product Management' }
  ];

  const tabs = [
    { id: 'products', label: 'Products', icon: 'icon-package' },
    { id: 'value-combos', label: 'Value Combos', icon: 'icon-layers' },
    { id: 'product-sets', label: 'Product Sets', icon: 'icon-grid-3x3' }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'products':
        return <ProductsTab />;
      case 'value-combos':
        return <ValueCombosTab />;
      case 'product-sets':
        return <ProductSetsTab />;
      default:
        return <ProductsTab />;
    }
  };

  return (
    <PrivateLayout>
      <div className="flex bg-surface-100 rounded-xl ml-3 w-[calc(100% - 12px)] mt-[60px]">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        <div
          className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full p-6 mx-auto rounded-s-xl rounded-bl-xl overflow-auto h-[calc(100vh-60px)] transition-base`}
        >
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <div className="flex flex-col gap-1">
              <h1 className="text-xl font-bold">Product Management</h1>
              <Breadcrumb items={breadcrumbItems} />
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="flex items-center gap-1 mb-4 bg-white p-1 rounded-xl border border-border-color w-fit">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-base ${
                  activeTab === tab.id
                    ? 'bg-primary-500 text-white'
                    : 'text-gray-400 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                <span className={`${tab.icon} text-base`} />
                {tab.label}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div className="flex-1 card">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
};

export default ProductManagement;
