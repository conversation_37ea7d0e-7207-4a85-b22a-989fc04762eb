'use client';

import React from 'react';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';

const InfoRow = ({ label, value }) => (
  <div className="flex flex-col gap-1">
    <span className="text-sm text-gray-500/60 font-normal">{label}</span>
    <span className="text-sm font-medium">{value || 'N/A'}</span>
  </div>
);

const ProductSetDetailModal = ({ isOpen, onClose, productSet }) => {
  if (!productSet) return null;

  const categories = [...new Set(productSet.products.map(p => p.category))];
  const totalValue = productSet.products.reduce((sum, product) => sum + product.price, 0);

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title={`Product Set Details - ${productSet.setCode}`}
      size="md"
    >
      <div className="space-y-6 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
        {/* General Information */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">General Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Set Name" value={productSet.setName} />
            <InfoRow label="Set Code" value={productSet.setCode} />
            <InfoRow label="Set Type" value={productSet.setType} />
            <InfoRow label="Display Type" value={productSet.displayType} />
            <InfoRow label="Total Products" value={`${productSet.totalProducts} items`} />
            <InfoRow label="Status" value={
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                productSet.status === 'Published' ? 'bg-green-100 text-green-800' :
                productSet.status === 'Draft' ? 'bg-gray-100 text-gray-800' :
                productSet.status === 'Pending Approval' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {productSet.status}
              </span>
            } />
          </div>
        </div>

        <hr className="border-gray-200" />

        {/* Description */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">Description</h4>
          <div className="space-y-3">
            <InfoRow label="Short Description" value={productSet.shortDescription} />
            {productSet.tags && productSet.tags.length > 0 && (
              <div className="flex flex-col gap-1">
                <span className="text-sm text-gray-500/60 font-normal">Tags</span>
                <div className="flex flex-wrap gap-2">
                  {productSet.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="inline-flex px-2 py-1 text-xs font-medium bg-info-500/20 text-info-500 rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        <hr className="border-gray-200" />

        {/* Set Configuration */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">Set Configuration</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow 
              label="Set Type Details" 
              value={
                productSet.setType === 'Related Products' ? 'Products that complement each other' :
                productSet.setType === 'Cross-sell' ? 'Alternative products to suggest' :
                productSet.setType === 'Upsell' ? 'Higher-value alternatives' :
                productSet.setType === 'Accessory Bundle' ? 'Essential accessories for main product' :
                'Seasonal or themed product collection'
              } 
            />
            <InfoRow label="Display Location" value="Product detail pages, cart, checkout" />
            <InfoRow label="Display Priority" value="Medium" />
            <InfoRow label="Auto-suggest" value="Enabled" />
          </div>
        </div>

        <hr className="border-gray-200" />

        {/* Categories Covered */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">Categories Covered</h4>
          <div className="flex flex-wrap gap-2">
            {categories.map((category, index) => (
              <span
                key={index}
                className="inline-flex px-3 py-1 text-sm font-medium bg-purple-100 text-purple-800 rounded-full"
              >
                {category}
              </span>
            ))}
          </div>
          <div className="mt-3">
            <InfoRow label="Primary Category" value={categories[0]} />
            <InfoRow label="Cross-category Set" value={categories.length > 1 ? 'Yes' : 'No'} />
          </div>
        </div>

        <hr className="border-gray-200" />

        {/* Included Products */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">Included Products</h4>
          <div className="space-y-3">
            {productSet.products.map((product, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                    <span className="icon icon-package text-gray-400 text-sm" />
                  </div>
                  <div>
                    <div className="font-medium text-sm">{product.name}</div>
                    <div className="text-xs text-gray-500">{product.category}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium text-sm">${product.price}</div>
                  <div className="text-xs text-gray-500">Individual price</div>
                </div>
              </div>
            ))}
          </div>
          
          {/* Value Summary */}
          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Total Individual Value:</span>
                <span>${totalValue.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Average Product Price:</span>
                <span>${(totalValue / productSet.totalProducts).toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Price Range:</span>
                <span>
                  ${Math.min(...productSet.products.map(p => p.price)).toFixed(2)} - 
                  ${Math.max(...productSet.products.map(p => p.price)).toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        </div>

        <hr className="border-gray-200" />

        {/* Performance Metrics */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">Performance Metrics</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Times Displayed" value="1,247 times" />
            <InfoRow label="Click-through Rate" value="8.3%" />
            <InfoRow label="Conversion Rate" value="3.2%" />
            <InfoRow label="Products Sold via Set" value="127 items" />
            <InfoRow label="Revenue Attribution" value="$4,832.45" />
            <InfoRow label="Average Order Increase" value="$67.23" />
          </div>
        </div>

        <hr className="border-gray-200" />

        {/* Display Rules */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">Display Rules</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Show on Product Pages" value="Yes" />
            <InfoRow label="Show in Cart" value="Yes" />
            <InfoRow label="Show at Checkout" value="No" />
            <InfoRow label="Show on Homepage" value="No" />
            <InfoRow label="Mobile Display" value="Enabled" />
            <InfoRow label="Minimum Cart Value" value="No minimum" />
          </div>
        </div>

        <hr className="border-gray-200" />

        {/* Targeting & Personalization */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">Targeting & Personalization</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Customer Segments" value="All customers" />
            <InfoRow label="Geographic Targeting" value="All regions" />
            <InfoRow label="Seasonal Display" value="Year-round" />
            <InfoRow label="Personalization" value="Based on browsing history" />
            <InfoRow label="A/B Testing" value="Not active" />
            <InfoRow label="Priority Score" value="75/100" />
          </div>
        </div>

        <hr className="border-gray-200" />

        {/* Meta Information */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">Meta Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow 
              label="Created Date" 
              value={new Date(productSet.createdAt).toLocaleDateString()} 
            />
            <InfoRow 
              label="Last Updated" 
              value={new Date(productSet.updatedAt).toLocaleDateString()} 
            />
            <InfoRow label="Created By" value="Admin User" />
            <InfoRow label="Last Modified By" value="Admin User" />
            <InfoRow label="Version" value="1.2" />
            <InfoRow label="Last Performance Review" value="2024-01-15" />
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between gap-3 border-t border-border-color p-4">
        <button
          onClick={onClose}
          className="btn btn-outline-gray"
        >
          Close
        </button>
        <div className="flex gap-3">
          <button className="btn btn-gray">
            View Analytics
          </button>
          <button className="btn btn-primary">
            Edit Set
          </button>
        </div>
      </div>
    </BaseOffCanvas>
  );
};

export default ProductSetDetailModal;
