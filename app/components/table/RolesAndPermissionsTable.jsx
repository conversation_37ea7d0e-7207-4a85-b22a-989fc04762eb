import React, { useState } from 'react';
import DataTable from 'react-data-table-component';
import { Tooltip } from 'react-tooltip';
import TableSkeleton from './TableSkeleton';
import AddNewMemberModal from '../modals/AddNewMemberModal';
import DeleteModal from '../modals/DeleteModal';
import SortIcon from './SortIcon';

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));
CustomCheckbox.displayName = 'CustomCheckbox';

const RolesAndPermissionsTable = ({ data = [], isLoading, isModalOpen, setIsModalOpen, paginationPerPage, selectedLocation, setSelectedLocation }) => {
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  
  const skeletonColumns = [
    { size: 'large', grow: 2 },
    { size: 'small', grow: 2 },
    { size: 'small', grow: 1 },
    { size: 'medium', grow: 1 },
    { size: 'small', grow: 1 },
    { size: 'small', grow: 1 },
    { size: 'icon' }
  ];
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const columns = [
    {
      name: 'Role',
      selector: (row) => row?.role || '-',
      sortable: true,
      reorder: true,
      grow: 2,
      cell: (row) => (
        <span className="font-semibold text-primary-500 text-sm">{row?.role || '-'}</span>
      ),
    },
    {
      name: 'Description',
      selector: (row) => row?.description || '-',
      sortable: false,
      reorder: true,
      grow: 2,
    },
    {
      name: 'User Count',
      selector: (row) => row?.userCount || 0,
      sortable: false,
      reorder: true,
    },
    {
      name: 'Created By',
      selector: (row) => row?.createdBy || '-',
      sortable: true,
      reorder: true,
    },
    {
      name: 'Created On',
      selector: (row) => row?.createdAt || '-',
      sortable: true,
      reorder: true,
    },
    {
      name: 'Status',
      selector: (row) => row?.status || '-',
      sortable: true,
      reorder: true,
      cell: (row) => (
        <span
          className={`px-2 py-1 rounded-lg text-xs font-semibold ${
            row?.status === 'Active'
              ? 'bg-success-500/10 text-success-500'
              : 'bg-gray-500/10 text-gray-500'
          }`}
        >
          {row?.status || '-'}
        </span>
      ),
    },
    {
      name: 'Actions',
      grow: 0,
      cell: (row) => (
        <>
          <button
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-gray-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
            data-tooltip-id="edit-tooltip"
            data-tooltip-content="Edit"
            onClick={() => {
              setSelectedLocation(row);
              setIsModalOpen(true);
            }}
          >
            <span className="icon icon-pencil-line text-base" />
          </button>

          <button
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
            data-tooltip-id="delete-tooltip"
            data-tooltip-content="Delete"
            onClick={() => {
              setSelectedLocation(row);
              setIsDeleteModalOpen(true);
            }}
          >
            <span className="icon icon-trash text-base" />
          </button>
          <Tooltip id="edit-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="delete-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </>
      ),
      sortable: false,
    },
  ];

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
    // Add styles for checkbox column
    selectableRows: {
      style: {
        width: '42px',
        paddingLeft: '8px',
        paddingRight: '0',
      },
    },
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  return (
    <>
      {isLoading ? (
        <TableSkeleton 
          rowsPerPage={paginationPerPage}
          columns={skeletonColumns}
          showCheckbox={true}
          cellHeight={4}
        />
      ) : (
        <DataTable
          columns={columns}
          data={data.length ? data : sampleData}
          customStyles={customStyles}
          pagination={false}
          paginationPerPage={paginationPerPage}
          selectableRows
          selectableRowsComponent={CustomCheckbox}
          selectableRowsComponentProps={{ 'aria-label': 'Select Row' }}
          selectableRowsHighlight
          className="custom-table auto-height-table"
          sortIcon={<SortIcon sortDirection={sortDirection} />}
          onSort={handleSort}
          sortField={sortedField}
          defaultSortAsc={true}
        />
      )}

      <AddNewMemberModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        location={selectedLocation}
      />
      <DeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        location={selectedLocation}
        title="Remove Team Member"
        message="You are about to remove this team member’s access to your vendor account. They will no longer be able to log in or perform any activities. This action cannot be undone."
        confirmButtonText="Remove user"
        confirmButtonClass="btn btn-danger"
        onConfirm={() => {
          // Handle remove user logic here
        }}
      />  
    </>
  );
};

export default RolesAndPermissionsTable;
