'use client';

import React, { useEffect, useRef, useState } from 'react';
import Form from 'next/form';
import Link from 'next/link';
import Image from 'next/image';
import InputField from '../Inputs/InputField';
import { Formik } from 'formik';
import { LOGIN_SCHEMA } from '@/utils/schema';
import {
  DASHBOARD,
  FORGET_PASSWORD,
  HOME,
} from '@/routes/urls';
import { useDispatch } from 'react-redux';
import { loginUserAsync } from '@/store/api/authApi';
import { useRouter } from 'next/navigation';
import ReCAPTCHA from 'react-google-recaptcha';
import { GOOGLE_RECAPTHA_API_KEY } from '../config';
import GoogleTwoFactor from './GoogleTwoFactor';
import CustomCheckbox from '../Inputs/CustomCheckbox';

export default function Login() {
  const recaptchaRef = useRef();
  const dispatch = useDispatch();
  const router = useRouter();
  const [show2FA, setShow2FA] = useState(false);
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [user, setUser] = useState('');
  const [loginAttempt, setLoginAttempt] = useState(0);
  const [remainingTime, setRemainingTime] = useState('00:00');

  return (
    <div className="flex items-stretch justify-between h-full min-h-screen">
      <div className="relative flex-[0_0_calc(100%_-_50%)] p-12 hidden lg:flex items-center justify-center min-h-screen">
        <div className="relative w-full max-w-[600px] aspect-square">
          <Image
            src="/images/welcome-img1.svg"
            alt="welcome image"
            fill
            className="object-contain"
            priority
          />
        </div>
        <div className="absolute top-0 left-0 -z-10 w-full h-full">
          <svg
            className="w-full h-full"
            viewBox="0 0 1920 1080"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g opacity="0.2" filter="url(#filter0_f)">
              <path
                fillRule="evenodd"
                d="M1461.49 1439.38C1242.66 1439.38 1063.61 1260.34 1063.61 1041.5V38.5C1063.61 -180.34 1242.65 -359.38 1461.49 -359.38C1680.33 -359.38 1859.38 -180.32 1859.38 38.5V1041.5C1859.38 1260.33 1680.31 1439.38 1461.49 1439.38Z"
                fill="#C31A5A"
              />
              <path
                fillRule="evenodd"
                d="M60.62 38.51C60.62 -180.31 239.66 -359.38 458.5 -359.38H1461.49C1680.33 -359.38 1859.38 -180.32 1859.38 38.5C1859.38 257.34 1680.33 436.4 1461.5 436.4H458.5C239.68 436.4 60.62 257.33 60.62 38.51Z"
                fill="#006CB5"
              />
              <path
                fillRule="evenodd"
                d="M1461.49-359.38C1680.33-359.38 1859.38-180.32 1859.38 38.5C1859.38 257.34 1680.33 436.4 1461.5 436.4H1063.61V38.5C1063.61-180.34 1242.65-359.38 1461.49-359.38Z"
                fill="#322B6A"
              />
            </g>
            <defs>
              <filter
                id="filter0_f"
                x="-877.7"
                y="-1297.7"
                width="3675.4"
                height="3675.41"
                filterUnits="userSpaceOnUse"
                colorInterpolationFilters="sRGB"
              >
                <feGaussianBlur stdDeviation="469.16" />
              </filter>
            </defs>
          </svg>
        </div>
      </div>

      <div className="flex flex-col justify-center min-h-screen flex-1 p-6">
        {/* Logo */}
        <div className="flex justify-center mb-8">
          <Image
            src="/images/logo-slim.svg"
            alt="Hubsups Logo"
            width={180}
            height={40}
            className="object-contain"
            priority
          />
        </div>
        <div className="bg-white rounded-2xl py-6 px-8 border border-border-color shadow-[0px_6px_12px_0px_#1C27310D] w-full max-w-[500px] mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-xl font-semibold mb-1">
              Welcome back!
            </h1>
            <p className="text-sm text-gray-400">
              Please enter your credentials to sign in!
            </p>
          </div>
          {show2FA ? (
            <GoogleTwoFactor qrCodeUrl={qrCodeUrl} user={user} />
          ) : (
            <Formik
              initialValues={{
                businessEmailId: '',
                password: '',
              }}
              validationSchema={LOGIN_SCHEMA}
              onSubmit={async (values) => {
                const { businessEmailId, password } = values;
                const { payload } = await dispatch(
                  loginUserAsync({ email: businessEmailId, password })
                );
                if (payload?.status) {
                  router.push(DASHBOARD)
                }
                // setLoginAttempt(payload?.data?.attempts ?? 0);
                // if (payload?.data?.attempts === 5) {
                //   setRemainingTime(payload?.data?.remaining ?? userBlockedTime);
                // }
                // if (payload?.data?.requires_2fa) {
                //   setShow2FA(true);
                //   setQrCodeUrl(payload.data.qr_code_url);
                //   setUser(values);
                // } else if (!payload?.data?.requires_2fa) {
                //   setCookie(
                //     cookiesKey.onboardingStatus,
                //     btoa(payload.data.user.onboarding_status)
                //   );
                //   setCookie(
                //     cookiesKey.feedbackVendor,
                //     btoa(payload.data.user.feedback_status ? true : '')
                //   );
                //   setCookie(cookiesKey.tourStatus, true);
                //   if (
                //     payload.data.user.onboarding_status &&
                //     payload.data.user.onboarding_status !==
                //       onboardingStatus.DRAFT
                //   ) {
                //     router.push(DASHBOARD);
                //   } else {
                //     router.push(BUSINESS_DETAILS);
                //   }
                // } else {
                //   return;
                // }
              }}
            >
              {(formik) => (
                <Form action="/search" onSubmit={formik.handleSubmit}>
                  {loginAttempt > 0 && loginAttempt < 5 && (
                    <div className="flex gap-2 items-start p-4 rounded-xl bg-warning-500/5 text-warning-500 mb-9">
                      <span className="icon icon-info-fill text-lg" />
                      <div className="flex flex-col gap-1 ">
                        <span className="text-xs font-semibold">
                          {`You have made ${loginAttempt} failed ${loginAttempt > 1 ? 'attempts' : 'attempt'}`}
                        </span>
                        <span className="text-xs font-medium">
                          After 5 failed attempts, your account will be locked
                          for 15 minutes.
                        </span>
                      </div>
                    </div>
                  )}

                  {loginAttempt === 5 && (
                    <div className="flex gap-2 items-start p-4 rounded-xl bg-danger-500/5 text-danger-500 mb-9">
                      <span className="icon icon-lock-key-fill text-lg" />
                      <div className="flex flex-col gap-1 ">
                        <span className="text-xs font-semibold">
                          You’ve exceeded the maximum number of login attempts
                        </span>
                        <span className="text-xs font-medium">
                          {`Your account is locked for ${remainingTime} minutes. If it’s
                          urgent, try using ‘Forgot Password’`}
                        </span>
                      </div>
                    </div>
                  )}
                  <InputField
                    id="businessEmailId"
                    name="businessEmailId"
                    type="text"
                    label="Email Address"
                    required={false}
                    formik={formik}
                  />
                  <InputField
                    id="password"
                    name="password"
                    type="password"
                    label="Password"
                    required={false}
                    marginBottom="mb-3"
                    placeholder={null}
                    formik={formik}
                  />

                  <div className="flex justify-between items-center mb-5">
                    <CustomCheckbox
                      id="rememberMe"
                      name="rememberMe"
                      label="Remember me"
                      formik={formik}
                    />
                    <Link
                      href={FORGET_PASSWORD}
                      className="font-bold text-xs text-primary-500 underline hover:text-primary-600 hover:no-underline transition-base"
                    >
                      Forgot Password?
                    </Link>
                  </div>

                  <button type="submit" className="btn w-full">
                    Sign in 
                  </button>

                  {/* <div className="flex justify-center gap-1 mt-6 text-sm">
                    Don't have an account yet?
                    <Link
                      href={HOME}
                      className="font-semibold hover:text-primary-500 transition-base"
                    >
                      Sign up
                    </Link>
                  </div> */}
                  <ReCAPTCHA
                    ref={recaptchaRef}
                    size="invisible"
                    sitekey={GOOGLE_RECAPTHA_API_KEY}
                  />
                </Form>
              )}
            </Formik>
          )}
        </div>
      </div>
    </div>
  );
}
