'use client';
import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Tooltip } from 'react-tooltip';

const Sidebar = ({ isCollapsed, setIsCollapsed }) => {
  const pathname = usePathname();

  // Add useEffect for responsive collapse
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth <= 1200) {
        setIsCollapsed(true);
      } else {
        setIsCollapsed(false);
      }
    };

    // Set initial state
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, [setIsCollapsed]);

  // Add handleCollapse function inside the component
  const handleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  // Sidebar Menu Items
  const mainMenuItems = [
    {
      path: '/dashboard',
      label: 'Dashboard',
      icon: 'icon-squares-four',
      className: 'products-item',
    },
    {
      path: '/orders-management',
      label: 'Orders Management',
      icon: 'icon-bulk',
      className: 'create-product-item',
      submenu: [
        { path: '/retail-orders', label: 'Retail Orders', },
        { path: '/bulk-orders', label: 'Bulk Orders' },
        { path: '/procurement', label: 'Procurement' }
      ]
    },
    {
      path: '/catalogue-management',
      label: 'Catalogue Management',
      icon: 'icon-stack',
      className: 'create-product-item',
      submenu: [
        { path: '/service-categories', label: 'Service Categories' },
        { path: '/product-categories', label: 'Product Categories' },
        { 
          path: '/product-attributes', 
          label: 'Product Attributes',
          submenu: [
            { path: '/attributes-management', label: 'Attributes Management' },
            { path: '/category-attributes-mapping', label: 'Category Attributes Mapping' },
          ]
        }
      ]
    },
    {
      path: '/product-management',
      label: 'Product Management',
      icon: 'icon-package-lined',
      className: 'create-product-item',
      submenu: [
        { path: '/manage-products', label: 'Manage Products' },
        { path: '/product-reviews', label: 'Product Reviews' }
      ]
    },
    {
      path: '/vendor-management',
      label: 'Vendor Management',
      icon: 'icon-storefront',
      className: 'create-product-item',
    },
    {
      path: '/buyer-management',
      label: 'Buyer Management',
      icon: 'icon-product-lisiting',
      className: 'create-product-item',
      submenu: [
        { path: '/buyer-directory', label: 'Buyer Directory' },
      ]
    },
    {
      path: '/inventory',
      label: 'Inventory & Warehouse',
      icon: 'icon-warehouse',
      className: 'create-product-item',
    },
    {
      path: '/logistics-management',
      label: 'Logistics Management',
      icon: 'icon-truck',
      className: 'create-product-item',
    },
    {
      path: '/finance-accounting',
      label: 'Finance & Accounting',
      icon: 'icon-bank',
      className: 'create-product-item',
      submenu: [
        { path: '/finance/1', label: 'E-commerce transactions' },
        { path: '/finance/2', label: 'Design Services' },
        { path: '/finance/3', label: 'Procurement Financial' },
        { path: '/finance/4', label: 'Transactions & Disbursements' },
      ]
    },
    {
      path: '/leads-projects',
      label: 'Leads & Projects',
      icon: 'icon-briefcase',
      className: 'create-product-item',
      submenu: [
        { path: '/lead-directory', label: 'All Leads' },
        { path: '/follow-up-tracker', label: 'Follow-up Tracker' },
        { path: '/project-list', label: 'Project List' },
        { path: '/project-assignments', label: 'Project Assignments' },
        { path: '/milestone-tracker', label: 'Milestone Tracker' },
      ]
    },
    {
      path: '/communication-center',
      label: 'Communication Center',
      icon: 'icon-address-book',
      className: 'create-product-item',
      submenu: [
        { path: '/internal-messaging', label: 'Internal Messaging' },
        { path: '/broadcast-announcements', label: 'Broadcast Announcements' },
      ]
    },
    {
      path: '/marketing-promotions',
      label: 'Marketing & Promotions',
      icon: 'icon-adv',
      className: 'create-product-item',
      submenu: [
        { path: '/campaign-management', label: 'Campaign Management' },
        { path: '/promotions-discounts', label: 'Promotions & Discounts' },
        { path: '/ad-banner-requests', label: 'Ad & Banner Requests' },
        { path: '/advertisement-rate-management', label: 'Advertisement Rate Management' },
      ]
    },
    {
      path: '/content-cms',
      label: 'Content & CMS',
      icon: 'icon-guide',
      className: 'create-product-item',
      submenu: [
        { path: '/page-cms', label: 'Page CMS' },
        { path: '/email-template-library', label: 'Email Template Library' },
      ]
    },
  ];

  const generalMenuItems = [
    {
      path: '/master',
      label: 'Masters Management',
      icon: 'icon-sliders',
      className: 'create-product-item',
      submenu: [
        {
          path: '/general-masters',
          label: 'General Masters',
          submenu: [
            { path: '/industry-management', label: 'Industry Management' },
            { path: '/brand-franchise', label: 'Brand & Franchise' },
          ]
        },
        { 
          path: '/product-masters', 
          label: 'Product Masters',
          submenu: [
            { path: '/units-of-measure', label: 'Units of Measure' },
            { path: '/product-style', label: 'Product Style' },
            { path: '/manufacturer-brands', label: 'Manufacturer Brands' },
          ]
        }
      ]
    },
    {
      path: '/roles',
      label: 'Roles & Permissions',
      icon: 'icon-check-circle',
      className: 'create-product-item',
    },
    {
      path: '/subscription-packages',
      label: 'Subscription Packages',
      icon: 'icon-crown-simple',
      className: 'create-product-item',
    },
    {
      path: '/support-helpdesk',
      label: 'Support & Helpdesk',
      icon: 'icon-help',
      className: 'create-product-item',
    },
    
    {
      path: '/settings',
      label: 'Settings',
      icon: 'icon-gear',
      className: 'create-product-item',
      submenu: [
        { path: '/general', label: 'General' },
        { path: '/settings/4', label: 'Security & 2FA' },
        { path: '/activity-log', label: 'Activity Log' },
      ]
    },
  ];

  // Add this handler for toggling submenu
  // Update the expandedItems initial state to check current path
  const [expandedItems, setExpandedItems] = useState(() => {
    const initialState = {};
    const checkSubmenuPaths = (items) => {
      items.forEach(item => {
        if (item.submenu) {
          const isActive = pathname.startsWith(item.path) || 
            item.submenu.some(subItem => {
              if (subItem.submenu) {
                return subItem.submenu.some(innerItem => pathname.startsWith(innerItem.path));
              }
              return pathname.startsWith(subItem.path);
            });
          initialState[item.path] = isActive;
          
          // Check nested submenus
          item.submenu.forEach(subItem => {
            if (subItem.submenu) {
              initialState[subItem.path] = isActive || pathname.startsWith(subItem.path);
            }
          });
        }
      });
    };
    
    checkSubmenuPaths([...mainMenuItems, ...generalMenuItems]);
    return initialState;
  });

  useEffect(() => {
    setExpandedItems(prev => {
      const newState = { ...prev };
      const checkSubmenuPaths = (items) => {
        items.forEach(item => {
          if (item.submenu) {
            const isActive = pathname.startsWith(item.path) || 
              item.submenu.some(subItem => {
                if (subItem.submenu) {
                  return subItem.submenu.some(innerItem => pathname.startsWith(innerItem.path));
                }
                return pathname.startsWith(subItem.path);
              });
            if (isActive) {
              newState[item.path] = true;
            }
            
            // Check nested submenus
            item.submenu.forEach(subItem => {
              if (subItem.submenuInner) {
                const isSubActive = pathname.startsWith(subItem.path) ||
                  subItem.submenuInner.some(innerItem => pathname.startsWith(innerItem.path));
                if (isSubActive) {
                  newState[subItem.path] = true;
                }
              }
            });
          }
        });
      };
      
      checkSubmenuPaths([...mainMenuItems, ...generalMenuItems]);
      return newState;
    });
  }, [pathname]);

  const toggleSubmenu = (path) => {
    setExpandedItems(prev => ({
      ...prev,
      [path]: !prev[path]
    }));
  };

  // Helper function to check if menu item is active
  const isMenuItemActive = (item) => {
    if (item.submenu) {
      return item.submenu.some(subItem => pathname === subItem.path);
    }
    return pathname === item.path;
  };

  // Helper function to check if submenu item is active
  const isSubmenuItemActive = (path) => {
    return pathname === path;
  };

  const isSubmenuInnerActive = (path) => {
    return pathname === path;
  };

  return (
    <>
      <div
        className={`sidebar fixed top-[60px] z-50 w-full h-[calc(100dvh_-_60px)] py-4 px-3 bg-white border-r-1 border-border-color transition-base ${
          isCollapsed
            ? 'sm:min-w-[80px] sm:max-w-[80px] left-0'
            : 'min-w-[280px] max-w-[280px] left-0'
          }`}
      >
        <div className="flex flex-col justify-between h-full">
          <div className="max-h-screen overflow-y-auto">
            {/* Main Menu Section */}
            <div className={`mb-2 px-4 ${isCollapsed ? 'hidden' : ''}`}>
              <span className="text-xs font-semibold text-gray-300 uppercase">Main</span>
            </div>
            <ul className="side-menu text-sm font-medium">
              {mainMenuItems.map((item) => (
                <li key={item.path}>
                  <div className="flex items-center justify-center">
                    {item.submenu ? (
                      // For items with submenu, make the entire div clickable
                      <button
                        onClick={() => toggleSubmenu(item.path)}
                        className={`flex items-center ${item.className || ''} ${
                          isCollapsed ? 'px-2 h-9 w-9 justify-self-center' : 'w-full px-4'
                        } py-2 mb-0.5 rounded-lg hover:bg-surface-300 transition-base ${
                          isMenuItemActive(item) ? 'bg-surface-300 active' : ''
                        }`}
                        data-tooltip-id={
                          isCollapsed
                            ? `tooltip-${item.label.toLowerCase()}`
                            : undefined
                        }
                        data-tooltip-content={isCollapsed ? item.label : undefined}
                      >
                        <span className={`icon text-xl ${item.icon}`}></span>
                        <span
                          className={`inline-block transition-all pl-2 text-nowrap duration-300 ease-in-out ${
                            isCollapsed ? 'opacity-0 w-0 text-[0px]' : 'opacity-100'
                          }`}
                        >
                          {item.label}
                        </span>
                        {!isCollapsed && (
                          <span 
                            className={`icon icon-caret-down ml-auto transition-transform duration-300 ${
                              expandedItems[item.path] ? 'rotate-180' : ''
                            }`} 
                          />
                        )}
                      </button>
                    ) : (
                      // For items without submenu, keep the Link component
                      <Link
                        href={item.path}
                        className={`flex items-center ${item.className || ''} ${
                          isCollapsed ? 'px-2 h-9 w-9 justify-self-center' : 'w-full px-4'
                        } py-2 mb-0.5 rounded-lg hover:bg-surface-300 transition-base ${
                          isMenuItemActive(item) ? 'bg-surface-300 active' : ''
                        }`}
                        data-tooltip-id={
                          isCollapsed
                            ? `tooltip-${item.label.toLowerCase()}`
                            : undefined
                        }
                        data-tooltip-content={isCollapsed ? item.label : undefined}
                      >
                        <span className={`icon text-xl ${item.icon}`}></span>
                        <span
                          className={`inline-block pl-2 transition-all text-nowrap duration-300 ease-in-out ${
                            isCollapsed ? 'opacity-0 w-0 text-[0px]' : 'opacity-100'
                          }`}
                        >
                          {item.label}
                        </span>
                      </Link>
                    )}
                    <Tooltip
                      id={`tooltip-${item.label.toLowerCase()}`}
                      className="!shadow-lg !py-1 !px-2 !font-bold !rounded-sm !text-xs"
                      place={isCollapsed ? 'right' : 'top'}
                    >
                      {item.label}
                    </Tooltip>
                  </div>
                  {/* Submenu list */}
                  {!isCollapsed && item.submenu && (
                    <ul className={`ml-2 overflow-hidden transition-all duration-300 ease-in-out ${
                      expandedItems[item.path] 
                        ? 'max-h-[500px] opacity-100 transform translate-y-0' 
                        : 'max-h-0 opacity-0 transform -translate-y-2'
                    }`}>
                      {item.submenu.map((subItem) => (
                        <li key={subItem.path}>
                          {subItem.submenu ? (
                            <>
                              <button
                                onClick={() => toggleSubmenu(subItem.path)}
                                className={`flex items-center w-full gap-3.5 px-4 py-1.5 text-sm rounded-lg group transition-base ${
                                  isSubmenuItemActive(subItem.path) ? 'active text-dark-500' : ''
                                }`}
                              >
                                <span className={`inline-block w-1.5 h-1.5 rounded-full ${
                                  isSubmenuItemActive(subItem.path) ? 'bg-dark-500' : 'bg-gray-200'
                                } group-hover:bg-dark-500 transition-all text-nowrap duration-300 ease-in-out`} />
                                <span className={isSubmenuItemActive(subItem.path) ? 'text-dark-500' : 'text-gray-400 group-hover:text-dark-500'}>
                                  {subItem.label}
                                </span>
                                <span 
                                  className={`icon icon-caret-down ml-auto transition-transform duration-300 ${
                                    expandedItems[subItem.path] ? 'rotate-180' : ''
                                  }`} 
                                />
                              </button>
                              {/* Nested Submenu */}
                              {!isCollapsed && subItem.submenu && (
                                <ul className={`ml-2 overflow-hidden transition-all duration-300 ease-in-out ${
                                  expandedItems[subItem.path] 
                                    ? 'max-h-[500px] opacity-100 transform translate-y-0' 
                                    : 'max-h-0 opacity-0 transform -translate-y-2'
                                }`}>
                                  {subItem.submenu.map((innerItem) => (
                                    <li key={innerItem.path}>
                                      <Link
                                        href={innerItem.path}
                                        className={`flex items-center gap-3.5 px-4 pl-5 py-1.5 text-sm rounded-lg group transition-base ${
                                          isSubmenuItemActive(innerItem.path) ? 'active text-dark-500' : ''
                                        }`}
                                      >
                                        <span className={`inline-block w-2 h-0.5 rounded-0 ${
                                          isSubmenuItemActive(innerItem.path) ? 'bg-dark-500' : 'bg-gray-200'
                                        } group-hover:bg-dark-500 transition-all text-nowrap duration-300 ease-in-out`} />
                                        <span className={isSubmenuItemActive(innerItem.path) ? 'text-dark-500' : 'text-gray-400 group-hover:text-dark-500'}>
                                          {innerItem.label}
                                        </span>
                                      </Link>
                                    </li>
                                  ))}
                                </ul>
                              )}
                            </>
                          ) : (
                            <Link
                              href={subItem.path}
                              className={`flex items-center gap-3.5 px-4 py-1.5 text-sm rounded-lg group transition-base ${
                                isSubmenuItemActive(subItem.path) ? 'active text-dark-500' : ''
                              }`}
                            >
                              <span className={`inline-block w-1.5 h-1.5 rounded-full ${
                                isSubmenuItemActive(subItem.path) ? 'bg-dark-500' : 'bg-gray-200'
                              } group-hover:bg-dark-500 transition-all text-nowrap duration-300 ease-in-out`} />
                              <span className={isSubmenuItemActive(subItem.path) ? 'text-dark-500' : 'text-gray-400 group-hover:text-dark-500'}>
                                {subItem.label}
                              </span>
                            </Link>
                          )}
                        </li>
                      ))}
                    </ul>
                  )}
                </li>
              ))}
            </ul>

            {/* General Section */}
            <div className={`mt-4 mb-2 border-t border-border-color pt-4 px-4 ${isCollapsed ? 'hidden' : ''}`}>
              <span className="text-xs font-semibold text-gray-300 uppercase">General</span>
            </div>
            <ul className="side-menu text-sm font-medium">
              {generalMenuItems.map((item) => (
                <li key={item.path}>
                  <div className="flex items-center justify-center">
                    {item.submenu ? (
                      // For items with submenu, make the entire div clickable
                      <button
                        onClick={() => toggleSubmenu(item.path)}
                        className={`flex items-center ${item.className || ''} ${
                          isCollapsed ? 'px-2 h-9 w-9 justify-self-center' : 'w-full px-4'
                        } py-2 mb-0.5 rounded-lg hover:bg-surface-300 transition-base ${
                          isMenuItemActive(item) ? 'bg-surface-300 active' : ''
                        }`}
                        data-tooltip-id={
                          isCollapsed
                            ? `tooltip-${item.label.toLowerCase()}`
                            : undefined
                        }
                        data-tooltip-content={isCollapsed ? item.label : undefined}
                      >
                        <span className={`icon text-xl ${item.icon}`}></span>
                        <span
                          className={`inline-block transition-all pl-2 text-nowrap duration-300 ease-in-out ${
                            isCollapsed ? 'opacity-0 w-0 text-[0px]' : 'opacity-100'
                          }`}
                        >
                          {item.label}
                        </span>
                        {!isCollapsed && (
                          <span 
                            className={`icon icon-caret-down ml-auto transition-transform duration-300 ${
                              expandedItems[item.path] ? 'rotate-180' : ''
                            }`} 
                          />
                        )}
                      </button>
                    ) : (
                      // For items without submenu, keep the Link component
                      <Link
                        href={item.path}
                        className={`flex items-center ${item.className || ''} ${
                          isCollapsed ? 'px-2 h-9 w-9 justify-self-center' : 'w-full px-4'
                        } py-2 mb-0.5 rounded-lg hover:bg-surface-300 transition-base ${
                          isMenuItemActive(item) ? 'bg-surface-300 active' : ''
                        }`}
                        data-tooltip-id={
                          isCollapsed
                            ? `tooltip-${item.label.toLowerCase()}`
                            : undefined
                        }
                        data-tooltip-content={isCollapsed ? item.label : undefined}
                      >
                        <span className={`icon text-xl ${item.icon}`}></span>
                        <span
                          className={`inline-block pl-2 transition-all text-nowrap duration-300 ease-in-out ${
                            isCollapsed ? 'opacity-0 w-0 text-[0px]' : 'opacity-100'
                          }`}
                        >
                          {item.label}
                        </span>
                      </Link>
                    )}
                    <Tooltip
                      id={`tooltip-${item.label.toLowerCase()}`}
                      className="!shadow-lg !py-1 !px-2 !font-bold !rounded-sm !text-xs"
                      place={isCollapsed ? 'right' : 'top'}
                    >
                      {item.label}
                    </Tooltip>
                  </div>
                  {/* Submenu list */}
                  {!isCollapsed && item.submenu && (
                    <ul className={`ml-2 overflow-hidden transition-all duration-300 ease-in-out ${
                      expandedItems[item.path] 
                        ? 'max-h-[500px] opacity-100 transform translate-y-0' 
                        : 'max-h-0 opacity-0 transform -translate-y-2'
                    }`}>
                      {item.submenu.map((subItem) => (
                        <li key={subItem.path}>
                          {subItem.submenu ? (
                            <>
                              <button
                                onClick={() => toggleSubmenu(subItem.path)}
                                className={`flex items-center w-full gap-3.5 px-4 py-1.5 text-sm rounded-lg group transition-base ${
                                  isSubmenuItemActive(subItem.path) ? 'active text-dark-500' : ''
                                }`}
                              >
                                <span className={`inline-block w-1.5 h-1.5 rounded-full ${
                                  isSubmenuItemActive(subItem.path) ? 'bg-dark-500' : 'bg-gray-200'
                                } group-hover:bg-dark-500 transition-all text-nowrap duration-300 ease-in-out`} />
                                <span className={isSubmenuItemActive(subItem.path) ? 'text-dark-500' : 'text-gray-400 group-hover:text-dark-500'}>
                                  {subItem.label}
                                </span>
                                <span 
                                  className={`icon icon-caret-down ml-auto transition-transform duration-300 ${
                                    expandedItems[subItem.path] ? 'rotate-180' : ''
                                  }`} 
                                />
                              </button>
                              {/* Nested Submenu */}
                              {!isCollapsed && subItem.submenu && (
                                <ul className={`ml-2 overflow-hidden transition-all duration-300 ease-in-out ${
                                  expandedItems[subItem.path] 
                                    ? 'max-h-[500px] opacity-100 transform translate-y-0' 
                                    : 'max-h-0 opacity-0 transform -translate-y-2'
                                }`}>
                                  {subItem.submenu.map((innerItem) => (
                                    <li key={innerItem.path}>
                                      <Link
                                        href={innerItem.path}
                                        className={`flex items-center gap-3.5 px-4 pl-5 py-1.5 text-sm rounded-lg group transition-base ${
                                          isSubmenuItemActive(innerItem.path) ? 'active text-dark-500' : ''
                                        }`}
                                      >
                                        <span className={`inline-block w-2 h-0.5 rounded-0 ${
                                          isSubmenuItemActive(innerItem.path) ? 'bg-dark-500' : 'bg-gray-200'
                                        } group-hover:bg-dark-500 transition-all text-nowrap duration-300 ease-in-out`} />
                                        <span className={isSubmenuItemActive(innerItem.path) ? 'text-dark-500' : 'text-gray-400 group-hover:text-dark-500'}>
                                          {innerItem.label}
                                        </span>
                                      </Link>
                                    </li>
                                  ))}
                                </ul>
                              )}
                            </>
                          ) : (
                            <Link
                              href={subItem.path}
                              className={`flex items-center gap-3.5 px-4 py-1.5 text-sm rounded-lg group transition-base ${
                                isSubmenuItemActive(subItem.path) ? 'active text-dark-500' : ''
                              }`}
                            >
                              <span className={`inline-block w-1.5 h-1.5 rounded-full ${
                                isSubmenuItemActive(subItem.path) ? 'bg-dark-500' : 'bg-gray-200'
                              } group-hover:bg-dark-500 transition-all text-nowrap duration-300 ease-in-out`} />
                              <span className={isSubmenuItemActive(subItem.path) ? 'text-dark-500' : 'text-gray-400 group-hover:text-dark-500'}>
                                {subItem.label}
                              </span>
                            </Link>
                          )}
                        </li>
                      ))}
                    </ul>
                  )}

                </li>
              ))}
            </ul>
          </div>

          {/* Settings & Other Links */}
          <div className="setting-wrapper mt-4">
            <div className={`flex justify-end items-center gap-1 ${isCollapsed ? 'flex-col' : ''} border-t border-dark-500/10 pt-2`} >
              {/* Collapse Side Navigation */}
              <button
                type="button"
                onClick={handleCollapse}
                data-tooltip-id="tooltip-collapse"
                data-tooltip-content={
                  isCollapsed ? 'Expand Side Nav' : 'Collapse Side Nav'
                }
                className="flex justify-center items-center h-9 w-9 p-2 cursor-pointer hover:bg-gray-500/10 rounded-lg transition-base"
              >
                <span
                  className={`icon text-xl icon-arrow-line-left align-middle transition-base ${isCollapsed ? 'rotate-180' : ''}`}
                />
              </button>
              <Tooltip
                id="tooltip-collapse"
                className="!shadow-lg !py-1 !px-2 !font-bold !rounded-sm !text-xs"
                place={isCollapsed ? 'right' : 'top'}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;