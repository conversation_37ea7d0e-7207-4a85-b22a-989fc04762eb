'use client';

import React, { useState, useRef } from 'react';
import DataTable from 'react-data-table-component';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import FilterField from '../../components/table/FilterField';
import SortableItem from '../../components/table/SortableItem';
import SortIcon from '../../components/table/SortIcon';
import TableSkeleton from '../../components/table/TableSkeleton';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';
import CommonPagination from '../../components/table/CommonPagination';
import { Tooltip } from 'react-tooltip';

const InfoRow = ({ label, value, className }) => (
  <div className={`flex flex-col gap-1 ${className}`}>
    <span className="text-sm text-gray-500/60 font-normal">{label}</span>
    <span className="text-sm text-dark-500 font-medium">{value}</span>
  </div>
);
const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const TrackingDetailModal = ({ isOpen, onClose, shipment }) => {
  if (!shipment) return null;

  const trackingEvents = [
    { date: '2024-01-15 14:30', status: 'Package picked up', location: 'Origin Facility', isLast: false },
    { date: '2024-01-15 18:45', status: 'In transit', location: 'Sorting Facility', isLast: false },
    { date: '2024-01-16 09:15', status: 'Out for delivery', location: 'Local Delivery Hub', isLast: false },
    { date: '2024-01-16 15:20', status: 'Delivered', location: 'Customer Address', isLast: true },
  ];

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title={`Shipment Tracking - ${shipment.shipmentId}`}
      size="sm"
    >
      <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
        {/* Shipment Information */}
        <div className="">
          <h4 className="font-semibold mb-3">Shipment Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Shipment ID" value={shipment.shipmentId} />
            <InfoRow label="Order ID" value={shipment.orderId} />
            <InfoRow label="Carrier" value={shipment.carrierName} />
            <InfoRow label="Tracking Number" value={shipment.trackingNumber} />
            <InfoRow label="Estimated Delivery Date" value={new Date(shipment.estimatedDeliveryDate).toLocaleDateString()} />
            <InfoRow label="Actual Delivery Date" value={shipment.actualDeliveryDate ? new Date(shipment.actualDeliveryDate).toLocaleDateString() : 'Pending'} />
          </div>
        </div>
        <hr/>

        {/* Destination Address */}
        <div className="">
          <h4 className="font-semibold mb-3">Destination Address</h4>
          <InfoRow label="Address" value={shipment.destinationAddress} />
        </div>
        <hr/>

        {/* Tracking Timeline */}
        <div className="">
          <h4 className="font-semibold mb-3">Tracking Timeline</h4>
          <div className="space-y-3">
            {trackingEvents.map((event, index) => (
              <div key={index} className="flex items-start gap-3 relative">
                <div className="w-3 h-3 bg-success-500 rounded-full mt-1 flex-shrink-0"></div>
                {!event.isLast && (
                  <div className="absolute top-[20px] left-[5px] w-[2px] h-[calc(100%-8px)] bg-success-500"></div>
                )}
                <div className="flex-1">
                  <div className="flex justify-between flex-col items-start">
                    <p className="font-medium text-sm">{event.status}</p>
                    <p className="text-xs mb-1 text-gray-300">{event.location}</p>
                    <p className="text-xs text-gray-500">{event.date}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-3 border-t border-border-color p-4">
        <button className="btn btn-primary">Update Status</button>
        <button className="btn btn-outline-gray">Mark as Delivered</button>
        <button className="btn btn-outline-gray">Retry Sync</button>
      </div>
    </BaseOffCanvas>
  );
};

const ShipmentTracking = ({ isLoading = false }) => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedShipment, setSelectedShipment] = useState(null);
  const [isTrackingDetailOpen, setIsTrackingDetailOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const displayMenuRef = useRef(null);

  const [displayProperties, setDisplayProperties] = useState({
    shipmentId: true,
    orderId: true,
    carrierName: true,
    trackingNumber: true,
    shipmentStatus: true,
    estimatedDeliveryDate: true,
    actualDeliveryDate: true,
    lastScanUpdate: true,
    destinationAddress: true,
  });

  const [items, setItems] = useState(Object.keys(displayProperties));

  // Sample data - in real app this would come from API
  const [shipments] = useState([
    {
      id: 'SHIP-001',
      shipmentId: 'SHIP-001',
      orderId: '#HSU-19384',
      carrierName: 'Bluedart',
      trackingNumber: 'BD123456789',
      shipmentStatus: 'In-Transit',
      estimatedDeliveryDate: '2024-01-16T17:00:00Z',
      actualDeliveryDate: null,
      lastScanUpdate: '2024-01-15T18:45:00Z',
      destinationAddress: '123 Main St, New York, NY 10001'
    },
    {
      id: 'SHIP-002',
      shipmentId: 'SHIP-002',
      orderId: '#HSU-19385',
      carrierName: 'Delhivery',
      trackingNumber: 'DL987654321',
      shipmentStatus: 'Delivered',
      estimatedDeliveryDate: '2024-01-15T16:00:00Z',
      actualDeliveryDate: '2024-01-15T15:30:00Z',
      lastScanUpdate: '2024-01-15T15:30:00Z',
      destinationAddress: '456 Oak Ave, Los Angeles, CA 90210'
    },
    {
      id: 'SHIP-003',
      shipmentId: 'SHIP-003',
      orderId: '#HSU-19386',
      carrierName: 'FedEx',
      trackingNumber: 'FX456789123',
      shipmentStatus: 'Pending',
      estimatedDeliveryDate: '2024-01-17T14:00:00Z',
      actualDeliveryDate: null,
      lastScanUpdate: '2024-01-15T10:00:00Z',
      destinationAddress: '789 Pine Rd, Chicago, IL 60601'
    },
    {
      id: 'SHIP-004',
      shipmentId: 'SHIP-004',
      orderId: '#HSU-19387',
      carrierName: 'UPS',
      trackingNumber: 'UP789123456',
      shipmentStatus: 'Failed',
      estimatedDeliveryDate: '2024-01-15T12:00:00Z',
      actualDeliveryDate: null,
      lastScanUpdate: '2024-01-15T11:30:00Z',
      destinationAddress: '321 Elm St, Houston, TX 77001'
    },
    {
      id: 'SHIP-005',
      shipmentId: 'SHIP-005',
      orderId: '#HSU-19388',
      carrierName: 'DHL',
      trackingNumber: 'DH654321987',
      shipmentStatus: 'Out for Delivery',
      estimatedDeliveryDate: '2024-01-16T18:00:00Z',
      actualDeliveryDate: null,
      lastScanUpdate: '2024-01-16T09:15:00Z',
      destinationAddress: '654 Maple Dr, Phoenix, AZ 85001'
    }
  ]);

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'in-transit':
        return 'bg-info-500/20 text-info-500';
      case 'out for delivery':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns = [
    {
      name: 'Shipment ID',
      selector: (row) => row.shipmentId,
      sortable: true,
      omit: !displayProperties.shipmentId,
    },
    {
      name: 'Order ID',
      selector: (row) => row.orderId,
      sortable: true,
      omit: !displayProperties.orderId,
    },
    {
      name: 'Carrier Name',
      selector: (row) => row.carrierName,
      sortable: true,
      omit: !displayProperties.carrierName,
    },
    {
      name: 'Tracking Number',
      selector: (row) => row.trackingNumber,
      sortable: true,
      omit: !displayProperties.trackingNumber,
    },
    {
      name: 'Shipment Status',
      selector: (row) => row.shipmentStatus,
      sortable: true,
      omit: !displayProperties.shipmentStatus,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold text-nowrap rounded-full ${getStatusColor(row.shipmentStatus)}`}>
          {row.shipmentStatus}
        </span>
      ),
    },
    {
      name: 'Estimated Delivery Date',
      selector: (row) => row.estimatedDeliveryDate,
      sortable: true,
      omit: !displayProperties.estimatedDeliveryDate,
      cell: (row) => new Date(row.estimatedDeliveryDate).toLocaleDateString(),
    },
    {
      name: 'Actual Delivery Date',
      selector: (row) => row.actualDeliveryDate,
      sortable: true,
      omit: !displayProperties.actualDeliveryDate,
      cell: (row) => row.actualDeliveryDate ? new Date(row.actualDeliveryDate).toLocaleDateString() : '-',
    },
    {
      name: 'Last Scan Update',
      selector: (row) => row.lastScanUpdate,
      sortable: true,
      omit: !displayProperties.lastScanUpdate,
      cell: (row) => new Date(row.lastScanUpdate).toLocaleString(),
    },
    {
      name: 'Destination Address',
      selector: (row) => row.destinationAddress,
      sortable: true,
      omit: !displayProperties.destinationAddress,
      cell: (row) => (
        <div className="text-sm max-w-[200px] truncate" title={row.destinationAddress}>
          {row.destinationAddress}
        </div>
      ),
    },
    {
      name: 'Actions',
      cell: (row) => (
        <div className="flex items-end justify-end gap-2">
          <button
            onClick={() => handleViewTracking(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Details"
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            {/* View Tracking */}
            <span className="icon icon-eye text-base" />
          </button>
          {row.shipmentStatus !== 'Delivered' && (
            <button 
              data-tooltip-id="mark-delivered-tooltip"
              data-tooltip-content="Mark as Delivered"
              className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-success-500/10 hover:text-success-500 rounded-lg cursor-pointer transition-base"
              >
              {/* Mark Delivered */}
              <span className="icon icon-check-3 text-base" />
            </button>
          )}
          <button 
            data-tooltip-id="update-status-tooltip"
            data-tooltip-content="Update Status"
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-primary-500/10 hover:text-primary-500 rounded-lg cursor-pointer transition-base"
            >
            {/* Update Status */}
            <span className="icon icon-pencil-line text-base" />
          </button>
          {/* <button className="text-blue-500 hover:text-blue-600 font-medium text-sm">
            <span className="icon icon-refresh-ccw text-base" />Retry Sync
          </button> */}

          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="mark-delivered-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="update-status-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      // allowOverflow: true,
      // button: true,
      width: '150px',
    },
  ];

  const handleViewTracking = (shipment) => {
    setSelectedShipment(shipment);
    setIsTrackingDetailOpen(true);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);
        const newItems = [...items];
        newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, active.id);
        return newItems;
      });
    }
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  const filteredShipments = shipments.filter((shipment) =>
    shipment.shipmentId.toLowerCase().includes(filterText.toLowerCase()) ||
    shipment.orderId.toLowerCase().includes(filterText.toLowerCase()) ||
    shipment.carrierName.toLowerCase().includes(filterText.toLowerCase()) ||
    shipment.trackingNumber.toLowerCase().includes(filterText.toLowerCase())
  );

  return (
    <div className="space-y-4">
      <div className="rounded-xl">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
        >
          <div className="absolute top-full right-0 mt-1 w-[200px] bg-white rounded-xl shadow-custom p-2 z-20">
            <DndContext
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={items}
                strategy={verticalListSortingStrategy}
              >
                <ul className="space-y-1">
                  {items.map((key) => (
                    <SortableItem
                      key={key}
                      id={key}
                      value={key}
                      checked={displayProperties[key]}
                      onChange={() =>
                        setDisplayProperties((prev) => ({
                          ...prev,
                          [key]: !prev[key],
                        }))
                      }
                    />
                  ))}
                </ul>
              </SortableContext>
            </DndContext>
          </div>
        </FilterField>

        {isLoading ? (
          <TableSkeleton 
            rowsPerPage={8}
            columns={[
              { size: 'small', grow: 1 },
              { size: 'small' },
              { size: 'medium' },
              { size: 'medium' },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'medium' },
              { size: 'large', grow: 1 },
              { size: 'large' },
            ]}
            showCheckbox={true}
            cellHeight={4}
          />
        ) : (
          <DataTable
            columns={columns}
            data={filteredShipments}
            customStyles={customStyles}
            pagination
            selectableRows
            fixedHeader={true}
            selectableRowsHighlight
            selectableRowsComponent={CustomCheckbox}
            onSelectedRowsChange={handleSelectedRowsChange}
            selectableRowsComponentProps={{ 'aria-label': 'Select Row' }}
            className="custom-table auto-height-table"
            sortIcon={<SortIcon sortDirection={sortDirection} />}
            onSort={handleSort}
            sortField={sortedField}
            defaultSortAsc={true}
            paginationPerPage={8}
            paginationRowsPerPageOptions={[8]}
            paginationComponentOptions={{
              rowsPerPageText: 'Rows per page:',
              rangeSeparatorText: 'of',
              selectAllRowsItem: false,
              noRowsPerPage: true,
            }}
            paginationComponent={(props) => (
              <CommonPagination
                selectedCount={props.selectedRows?.length}
                total={props.totalRows}
                page={props.currentPage}
                perPage={props.rowsPerPage}
                onPageChange={props.onChangePage}
              />
            )}
          />
        )}
      </div>

      {/* Tracking Detail Modal */}
      <TrackingDetailModal
        isOpen={isTrackingDetailOpen}
        onClose={() => setIsTrackingDetailOpen(false)}
        shipment={selectedShipment}
      />
    </div>
  );
};

export default ShipmentTracking;
