'use client';

import React, { useState } from 'react';
import PrivateLayout from '../components/layout/PrivateLayout';
import Sidebar from '../components/sidebar/Sidebar';
import Breadcrumb from '../components/Inputs/Breadcrumb';
import RetailOrderDashboard from './components/RetailOrderDashboard';
import RetailOrdersTable from './components/RetailOrdersTable';
import ProductReturns from './components/ProductReturns';
import ShipmentTracking from './components/ShipmentTracking';

export default function RetailOrders() {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [isLoading, setIsLoading] = useState(false);

  // Sample data - in real app this would come from API
  const [dashboardData, setDashboardData] = useState({
    metrics: {
      totalOrders: 1247,
      pendingOrders: 23,
      shippedOrders: 156,
      deliveredOrders: 1068,
      cancelledOrders: 12,
      returnRequests: 8,
      totalRevenue: 387500,
      averageOrderValue: 310.85
    },
    recentOrders: [
      {
        id: 'ORD-001',
        orderId: '#HSU-19384',
        orderDate: '2024-01-15T10:30:00Z',
        customerName: 'John Smith',
        email: '<EMAIL>',
        phone: '******-0123',
        orderAmount: 299.99,
        paymentStatus: 'Paid',
        fulfillmentStatus: 'Processing',
        shippingMethod: 'Standard',
        orderSource: 'Website'
      },
      {
        id: 'ORD-002',
        orderId: '#HSU-19385',
        orderDate: '2024-01-15T09:15:00Z',
        customerName: 'Sarah Johnson',
        email: '<EMAIL>',
        phone: '******-0124',
        orderAmount: 156.50,
        paymentStatus: 'Paid',
        fulfillmentStatus: 'Shipped',
        shippingMethod: 'Express',
        orderSource: 'Mobile App'
      },
      {
        id: 'ORD-003',
        orderId: '#HSU-19386',
        orderDate: '2024-01-15T08:45:00Z',
        customerName: 'Mike Davis',
        email: '<EMAIL>',
        phone: '******-0125',
        orderAmount: 89.99,
        paymentStatus: 'Pending',
        fulfillmentStatus: 'Processing',
        shippingMethod: 'Standard',
        orderSource: 'Marketplace'
      }
    ]
  });

  const tabs = [
    { id: 'dashboard', label: 'Order Dashboard', icon: 'icon-dashboard' },
    { id: 'orders', label: 'All Orders', icon: 'icon-shopping-bag' },
    { id: 'returns', label: 'Product Returns', icon: 'icon-refresh-ccw' },
    { id: 'tracking', label: 'Shipment Tracking', icon: 'icon-truck' }
  ];

  const breadcrumbItems = [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Retail Orders', href: '/retail-orders' }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <RetailOrderDashboard data={dashboardData} isLoading={isLoading} />;
      case 'orders':
        return <RetailOrdersTable isLoading={isLoading} />;
      case 'returns':
        return <ProductReturns isLoading={isLoading} />;
      case 'tracking':
        return <ShipmentTracking isLoading={isLoading} />;
      default:
        return <RetailOrderDashboard data={dashboardData} isLoading={isLoading} />;
    }
  };

  return (
    <PrivateLayout>
      <div className="flex rounded-xl mt-[60px] w-full">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        <div
          className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full mx-auto py-6 px-8 bg-surface-100 overflow-auto h-[calc(100dvh-60px)] transition-base`}
        >
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <div className="flex flex-col gap-1">
              <h1 className="text-xl font-bold">Retail Orders</h1>
              <Breadcrumb items={breadcrumbItems} />
            </div>
            <div className="flex gap-3">
              <button className="btn btn-outline-gray flex items-center gap-2">
                <span className="icon icon-upload-simple text-base" />
                Export
              </button>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="flex items-center gap-1 mb-4 bg-white p-1 rounded-xl border border-border-color w-fit">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-base ${
                  activeTab === tab.id
                    ? 'bg-primary-500 text-white'
                    : 'text-gray-400 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                {/* <span className={`${tab.icon} text-base`} /> */}
                {tab.label}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div className="flex-1">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
}
